<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="Type" v-show="false">
          <el-input v-model="queryParams.type" value="2"></el-input>
        </el-form-item>
        <el-form-item label="地区">
          <x-select
            v-model="queryParams.areaId"
            :options="areaOptions"
            @change="fetchTaskConfigOption"
          ></x-select>
        </el-form-item>
        <el-form-item label="任务">
          <x-select v-model="queryParams.taskConfigId" :options="taskConfigOptions"></x-select>
        </el-form-item>
        <el-form-item label="渠道标识">
          <el-input v-model="queryParams.cid"></el-input>
        </el-form-item>
        <el-form-item label="主号">
          <el-input v-model="queryParams.account"></el-input>
        </el-form-item>
        <el-form-item label="IP">
          <el-input v-model="queryParams.ip"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="taskName" label="任务名称/地区" align="center" min-width="200">
          <template v-slot="{ row }">
            <task-info
              :taskId="row.taskId"
              :taskName="row.taskName"
              :areaName="row.areaName"
            ></task-info>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.cId" :cName="row.cName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP/国家/城市" align="center" min-width="280">
          <template v-slot="{ row }">
            <div class="ip-location-info">
              <!-- IP地址 -->
              <div class="ip-row">
                <i class="el-icon-connection" style="color: #409eff; margin-right: 5px"></i>
                <el-tag type="primary" size="small" effect="light">{{ row.ip }}</el-tag>
              </div>

              <!-- 国家信息 -->
              <div v-if="row.country" class="location-row">
                <i class="el-icon-location" style="color: #67c23a; margin-right: 5px"></i>
                <el-tag type="success" size="mini" effect="plain">{{ row.country }}</el-tag>
              </div>

              <!-- 城市信息 -->
              <div v-if="row.city" class="location-row">
                <i class="el-icon-map-location" style="color: #e6a23c; margin-right: 5px"></i>
                <el-tag type="warning" size="mini" effect="plain">{{ row.city }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="120">
          <template v-slot="{ row }">
            <div class="status-column">
              <!-- IsIP 状态 -->
              <div class="status-item">
                <i
                  class="el-icon-cpu"
                  style="color: #909399; margin-right: 3px; font-size: 12px"
                ></i>
                <el-tag
                  :type="row.isIP ? 'success' : 'info'"
                  size="mini"
                  effect="dark"
                  class="status-tag"
                >
                  IP{{ row.isIP ? '✓' : '✗' }}
                </el-tag>
              </div>

              <!-- IsUV 状态 -->
              <div class="status-item">
                <i
                  class="el-icon-user"
                  style="color: #909399; margin-right: 3px; font-size: 12px"
                ></i>
                <el-tag
                  :type="row.isUV ? 'success' : 'info'"
                  size="mini"
                  effect="dark"
                  class="status-tag"
                >
                  UV{{ row.isUV ? '✓' : '✗' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="主号/像素" align="center" min-width="200">
          <template v-slot="{ row }">
            <div class="account-pixel-info">
              <!-- 主号信息 -->
              <div class="account-row">
                <i class="el-icon-user" style="color: #409eff; margin-right: 5px"></i>
                <el-tag type="primary" size="small" effect="light">{{
                  row.account || '无主号'
                }}</el-tag>
              </div>

              <!-- 像素信息 -->
              <div class="pixel-row">
                <i class="el-icon-view" style="color: #67c23a; margin-right: 5px"></i>
                <el-tag type="success" size="mini" effect="plain">{{
                  row.pixelId || '无像素'
                }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="utmSource"
          label="来源"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="createOn"
          label="入库时间"
          align="center"
          min-width="180"
        ></el-table-column>
        <el-table-column label="来源URL" align="center" min-width="300">
          <template slot-scope="scope">
            <div class="referrer-column">
              <!-- 有 Referrer URL 时 -->
              <div v-if="scope.row.referrer" class="referrer-content">
                <div class="referrer-url" :title="scope.row.referrer">
                  <a :href="scope.row.referrer" target="_blank">{{ scope.row.referrer }}</a>
                </div>
              </div>

              <!-- 无 Referrer 时的占位 -->
              <div class="no-referrer-content" v-else>
                <span class="no-referrer-text"> 无来源URL </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" align="center">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button size="mini" type="primary" @click="handleOpenWeb(scope.row, 'ipapi')"
                >ipapi</el-button
              >
              <el-button size="mini" type="primary" @click="handleOpenWeb(scope.row, 'ipinfo')"
                >ipinfo</el-button
              >
            </div>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageCollectRecordApi, deliverAreaApi, optionsApi } from '@/api'
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'

export default {
  components: {
    TaskInfo,
    ChannelInfo,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      areaOptions: [],
      taskConfigOptions: [],
    }
  },
  async created() {
    const query = this.$router.history.current.query
    if (query.date) this.queryParams.date = query.date
    if (query.type) this.queryParams.type = query.type
    if (query.areaId) this.queryParams.areaId = parseInt(query.areaId)
    if (query.taskConfigId) this.queryParams.taskConfigId = parseInt(query.taskConfigId)
    if (query.cid) this.queryParams.cid = query.cid
    if (query.account) this.queryParams.account = query.account
    if (query.ip) this.queryParams.ip = query.ip

    await this.fetchAreaOption()
  },
  watch: {
    'queryParams.areaId': {
      handler: 'fetchTaskConfigOption',
      immediate: true,
    },
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageCollectRecordApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async fetchAreaOption() {
      const res = await deliverAreaApi.getAreaOptions()
      if (res && res.code == 0) {
        this.areaOptions = res.data
      } else this.$xMsgError(res.msg)
    },
    async fetchTaskConfigOption() {
      const res = await optionsApi.getTaskConfigOptions({ areaId: this.queryParams.areaId })
      if (res && res.code == 0) {
        this.taskConfigOptions = res.data
      } else this.$xMsgError(res.msg)
    },
    handleOpenWeb(row, type) {
      const ip = row.ip
      if (type == 'ipapi') window.open(`https://ipapi.co/${ip}/json/`)
      else if (type == 'ipinfo') window.open(`https://ipinfo.io/${ip}/json`)
    },

    // 验证是否为有效的 URL
    isValidUrl(string) {
      try {
        new URL(string)
        return true
      } catch (_) {
        return false
      }
    },
  },
}
</script>

<style scoped>
.ip-location-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 4px;
}

.ip-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
}

.location-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px 0;
}

.status-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 4px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.status-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  font-weight: 600;
  min-width: 35px;
  text-align: center;
}

.ip-location-info .el-tag {
  font-weight: 500;
  border-radius: 4px;
}

.ip-location-info .el-icon-connection {
  font-size: 14px;
}

.ip-location-info .el-icon-location,
.ip-location-info .el-icon-map-location {
  font-size: 12px;
}

.ip-location-info .el-icon-cpu,
.ip-location-info .el-icon-user {
  font-size: 12px;
}

/* 鼠标悬停效果 */
.ip-location-info:hover .el-tag {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 状态标签特殊样式 */
.status-tag.el-tag--success.el-tag--dark {
  background-color: #67c23a;
  border-color: #67c23a;
  color: #fff;
}

.status-tag.el-tag--info.el-tag--dark {
  background-color: #909399;
  border-color: #909399;
  color: #fff;
}

/* 主号/像素列样式 */
.account-pixel-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 4px;
}

.account-row,
.pixel-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px 0;
}

/* 操作列样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Referrer 列样式 */
.referrer-column {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.referrer-content {
  width: 100%;
  display: flex;
  padding: 4px 0;
  flex-direction: column;
}

.referrer-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  word-break: break-all;
  word-wrap: break-word;
  display: flex;
  align-items: flex-start;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  max-height: 120px;
  overflow-y: auto;
}

.referrer-url i {
  flex-shrink: 0;
  margin-top: 1px;
}

.no-referrer-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.no-referrer-text {
  font-size: 12px;
  color: #c0c4cc;
  font-style: italic;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-buttons {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .referrer-url {
    font-size: 11px;
    padding: 6px 8px;
    max-height: 100px;
  }

  .referrer-column {
    padding: 6px 8px;
  }

  .account-pixel-info {
    padding: 6px 2px;
    gap: 4px;
  }

  .account-pixel-info .el-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}
</style>
