<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select show-default v-model="queryParams.areaId" url="/deliverArea/options"></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 页面统计异常提示 -->
      <el-row style="margin-bottom: 10px" v-if="alertInfoList.length > 0">
        <el-alert
          v-for="alertInfoList in alertInfoList"
          :key="alertInfoList.id"
          :title="alertInfoList.title"
          :type="alertInfoList.type"
          style="margin-bottom: 10px"
          @close="handleAlertClose(alertInfoList)"
        >
        </el-alert>
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        :header-cell-style="{
          //background: 'linear-gradient(90deg, #f5f7fa 60%, #e3eaf1 100%)',
          color: '#34495e',
          fontWeight: 'bold',
          fontSize: '15px',
          padding: '14px 0',
          letterSpacing: '1px',
        }"
        :cell-style="{
          fontSize: '14px',
          padding: '5px 0',
          background: '#fff',
        }"
        :row-class-name="tableRowClassName"
        border
        stripe
      >
        <el-table-column
          v-for="(col, index) in columns"
          :key="index"
          :prop="col.prop"
          :label="col.label"
          align="center"
          :min-width="col.width"
        >
          <template #default="scope">
            <!-- 合并3列数据，显示在同一列中 -->
            <div
              v-if="isChannelColumn(col.prop) && scope.row[`${col.prop}`] != null"
              class="channel-column"
            >
              <div class="column-item">
                <span class="label">进线：</span>
                <span class="value">
                  {{ scope.row[`${col.prop}`] }}
                </span>
              </div>
              <div class="column-item">
                <span class="label">容量：</span>
                <span class="value">{{
                  scope.row[`${col.prop.replace('channel', 'residue')}`]
                }}</span>
              </div>
              <div class="column-item">
                <span class="label">账号：</span>
                <span class="value">{{
                  scope.row[`${col.prop.replace('channel', 'accounts')}`]
                }}</span>
              </div>
              <div class="column-item">
                <span class="label">调粉：</span>
                <span class="value">{{
                  scope.row[`${col.prop.replace('channel', 'changeNum')}`]
                }}</span>
              </div>
              <div class="column-item">
                <span class="label">封号：</span>
                <span class="value">{{
                  scope.row[`${col.prop.replace('channel', 'bannedNum')}`]
                }}</span>
              </div>
            </div>
            <task-info
              v-else-if="col.prop === 'name' && scope.row[col.prop] != '合计'"
              :taskId="scope.row['id']"
              :taskName="scope.row[col.prop]"
              :areaName="scope.row['areaName']"
              class="task-info"
            ></task-info>
            <span
              v-else-if="col.prop === 'price' && scope.row[col.prop] != '--'"
              class="price-column"
            >
              <el-tag effect="dark" size="small">¥{{ scope.row[col.prop] }}</el-tag>
            </span>
            <span v-else-if="col.prop === 'repeatFansPro'" class="percent-column">
              <!--<el-progress
                :percentage="Number(scope.row[col.prop])"
                :stroke-width="14"
                color="#409eff"
                :show-text="false"
                style="width: 60px; vertical-align: middle"
              />-->
              <span style="margin-left: 8px">{{ scope.row[col.prop] }}%</span>
            </span>
            <span v-else-if="scope.row[col.prop] == '合计'" class="total-row">
              <el-icon style="vertical-align: middle; color: #f56c6c"
                ><i class="el-icon-s-flag"
              /></el-icon>
              <span style="margin-left: 4px">{{ scope.row[col.prop] }}</span>
            </span>
            <span v-else-if="col.prop == 'newFans'">
              {{ scope.row[col.prop] }}
              <span v-if="scope.row.orderDailyNewFans"
                ><br />
                <el-tag effect="dark" size="small">{{ scope.row.orderDailyNewFans }}</el-tag>
              </span>
            </span>
            <!-- 其他数据直接显示 -->
            <span v-else class="normal-cell">
              {{ scope.row[col.prop] }}
            </span>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
    <countdown ref="timer" :table="$refs.table"></countdown>
  </div>
</template>

<script>
import Countdown from '@/components/Page/Countdown'
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverStatsApi, systemNoticeApi } from '@/api'
//import EditDialog from './Edit'
export default {
  components: {
    //EditDialog
    TaskInfo,
    Countdown,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      columns: [],
      alertInfoList: [],
      fetchNotificationsTimer: null,
    }
  },
  async mounted() {
    this.fetchNotifications()
    this.fetchNotificationsTimer = setInterval(() => {
      this.fetchNotifications()
    }, 60 * 1000)
  },
  beforeRouteLeave(to, from, next) {
    clearInterval(this.fetchNotificationsTimer)
    this.$refs.timer.stopAutoRefresh() // 手动调用子组件方法
    next()
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getColumns() {
      this.$xloading.show()
      const res = await deliverStatsApi.getTaskStatColumns()
      if (res && res.code == 0) {
        this.columns = res.data
        console.log(this.columns)
      } else this.$xMsgError(res.msg)
      this.$xloading.hide()
    },
    async fetchNotifications() {
      const res = await systemNoticeApi.getNoticeInfo()
      if (res.code !== 0) return
      //this.alertInfoList = res.data.alertList
      this.$set(this, 'alertInfoList', res.data.alertList)
    },
    async getList(params) {
      this.$xloading.show()
      //await this.getColumns()
      params = Object.assign({}, params, this.queryParams)
      const res = await deliverStatsApi.getTaskStatsList(params)
      this.columns = res.columns
      this.tableData = res
      this.$xloading.hide()
    },
    handleAlertClose(data) {
      systemNoticeApi.readNotice(data.id, 3)
    },
    // 判断是否是 channel 列
    isChannelColumn(prop) {
      return prop.includes('-channel')
    },
    tableRowClassName({ row }) {
      if (row.name === '合计') {
        return 'total-row-class'
      }
      return ''
    },
  },
}
</script>

<style scoped>
.channel-column {
  display: flex;
  flex-direction: column;
}

.column-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(90deg, #f8f9fb 80%, #eaf6ff 100%);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(64, 158, 255, 0.04);
  transition: background 0.3s;
}

.column-item:hover {
  background: #e3f2fd;
}

.column-item .label {
  font-weight: 600;
  color: #409eff;
  font-size: 13px;
  letter-spacing: 0.5px;
}

.column-item .value {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  border-radius: 10px;
  padding: 2px 10px;
  min-width: 32px;
  text-align: right;
  background: #f4f8fb;
}

.price-column {
  font-weight: 600;
  font-size: 15px;
}

.percent-column {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-weight: 500;
}

.total-row {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.normal-cell {
  color: #606266;
  font-size: 14px;
}

.task-info {
  padding: 4px 0;
}

:deep(.total-row-class) {
  background-color: #fdf6ec !important;
}

:deep(.el-table) {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.07);
}

:deep(.el-table__header-wrapper) {
  background: linear-gradient(90deg, #f5f7fa 60%, #e3eaf1 100%);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f8fafc;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #e3f2fd !important;
}
</style>
