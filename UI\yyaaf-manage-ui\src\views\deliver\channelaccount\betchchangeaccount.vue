<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
    width="700px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <!-- 新主号输入 -->
      <el-form-item label="新主号" prop="accountText" style="margin-top: 20px">
        <el-input
          v-model="form.accountText"
          placeholder="请输入新的主号字符串"
          clearable
          type="textarea"
          :rows="10"
        >
          <template slot="prepend">
            <i class="el-icon-user"></i>
          </template>
        </el-input>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          如果是12位手机号码支持 ：************ 换 ************ 多个使用换行分隔
        </div>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          通用方法 ：************@************ 多个使用换行分隔
        </div>
      </el-form-item>
      <el-form-item label="换号类型" prop="changeAccountType">
        <x-radio v-model="form.changeAccountType" :options="changeAccountOptions" />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          新号继承旧号进线数: 当新号的计数器是从旧号的进线数开始计数是选择这个。
        </div>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          新号不继承旧号进线数: 当新号的计数器是从0开始计数是选择这个。
        </div>
      </el-form-item>
    </el-form>
  </x-dialog>
</template>

<script>
import { deliverTaskChannelAccountApi } from '@/api'

export default {
  data() {
    return {
      title: '批量换号操作',
      visible: false,
      loading: false,
      currentRow: null,
      form: {
        taskId: null,
        accountText: '',
        changeAccountType: 1,
      },
      changeAccountOptions: [
        { label: '新号继承旧号进线数', value: 1 },
        { label: '新号不继承旧号进线数', value: 2 },
      ],
      rules: {
        accountText: [{ required: true, message: '请输入新主号字符串', trigger: 'blur' }],
      },
    }
  },

  methods: {
    open(row) {
      this.currentRow = row
      this.form.taskId = row.taskId
      this.title = `批量换号操作 - ${this.currentRow.taskTitle}`
      this.visible = true
      this.resetForm()
    },

    resetForm() {
      this.form = {
        taskId: this.currentRow ? this.currentRow.taskId : null,
        accountText: '',
        changeAccountType: 1,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await this.handleChangeAccount()
        }
      })
    },

    async handleChangeAccount() {
      this.$confirm(`确认将进行批量换号吗？`, '换号批量确认', {
        type: 'warning',
        confirmButtonText: '确认批量换号',
        cancelButtonText: '取消',
      })
        .then(async () => {
          try {
            this.loading = true

            const data = {
              taskId: this.form.taskId,
              accountText: this.form.accountText,
              changeAccountType: this.form.changeAccountType,
            }

            const res = await deliverTaskChannelAccountApi.betchChange(data)

            if (res.code === 0) {
              this.$xMsgSuccess('批量换号成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('批量换号失败：' + res.msg)
            }
          } catch (error) {
            this.$xMsgError('批量换号失败：' + error.message)
          } finally {
            this.loading = false
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
  },
}
</script>

<style scoped>
.current-info-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 70px;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.form-tip i {
  margin-right: 4px;
}

/* 输入框样式增强 */
.el-input-group__prepend {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
}

/* 卡片内容样式 */
.current-info-card .el-card__body {
  padding: 16px;
}

/* 标签样式调整 */
.el-tag {
  font-weight: 500;
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 22px;
}

/* 对话框内容区域 */
.el-dialog__body {
  padding: 20px 20px 10px 20px;
}
</style>
