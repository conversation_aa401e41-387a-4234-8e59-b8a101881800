<template>
  <!-- 🔥 右侧浮动倒计时 -->
  <div class="floating-timer">
    <span class="timer-text">下次刷新:</span>
    <span class="timer-countdown">{{ countdown }}s</span>
  </div>
</template>

<script>
export default {
  props: ['table'], // 只接收 table 组件，不传 countdown
  data() {
    return {
      countdown: 60, // 确保每个实例独立维护倒计时
      refreshTimer: null,
    }
  },
  mounted() {
    this.startAutoRefresh()
  },
  beforeUnmount() {
    clearInterval(this.refreshTimer) // 组件销毁时清除定时器
  },
  methods: {
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.countdown <= 0) {
          if (this.table) {
            this.table.refresh(false) // 确保 table 存在
          }
          this.countdown = 60 // 重新开始倒计时
        } else {
          this.countdown--
        }
      }, 1000)
    },
    stopAutoRefresh() {
      clearInterval(this.refreshTimer) // 组件销毁时清除定时器
    },
  },
}
</script>

<style scoped>
/* 🔥 浮动倒计时 */
.floating-timer {
  position: fixed;
  top: 85%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 10px 15px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999;
}

.timer-text {
  font-size: 12px;
  margin-bottom: 5px;
  opacity: 0.8;
}

.timer-countdown {
  font-size: 18px;
  font-weight: bold;
  color: #ffeb3b;
}
</style>
