<template>
  <!-- 🔥 右侧浮动倒计时 -->
  <div class="floating-timer" :class="{ collapsed: isCollapsed }" @click="toggleCollapse">
    <!-- 展开状态 -->
    <div v-if="!isCollapsed" class="timer-content">
      <span class="timer-text">下次刷新:</span>
      <span class="timer-countdown">{{ countdown }}s</span>
      <i class="el-icon-minus collapse-icon" title="收起"></i>
    </div>

    <!-- 收起状态 -->
    <div v-else class="timer-collapsed">
      <i class="el-icon-time collapse-icon" title="展开倒计时"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: ['table'], // 只接收 table 组件，不传 countdown
  data() {
    return {
      countdown: 60, // 确保每个实例独立维护倒计时
      refreshTimer: null,
      isCollapsed: false, // 控制收起/展开状态
    }
  },
  mounted() {
    this.startAutoRefresh()
    // 从本地存储恢复收起状态
    const savedState = localStorage.getItem('countdown-collapsed')
    if (savedState !== null) {
      this.isCollapsed = savedState === 'true'
    }
  },
  beforeUnmount() {
    clearInterval(this.refreshTimer) // 组件销毁时清除定时器
  },
  methods: {
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.countdown <= 0) {
          if (this.table) {
            this.table.refresh(false) // 确保 table 存在
          }
          this.countdown = 60 // 重新开始倒计时
        } else {
          this.countdown--
        }
      }, 1000)
    },
    stopAutoRefresh() {
      clearInterval(this.refreshTimer) // 组件销毁时清除定时器
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      // 保存状态到本地存储
      localStorage.setItem('countdown-collapsed', this.isCollapsed)
    },
  },
}
</script>

<style scoped>
/* 🔥 浮动倒计时 */
.floating-timer {
  position: fixed;
  top: 85%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  font-size: 14px;
  z-index: 999;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.floating-timer:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 展开状态 */
.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  position: relative;
}

.timer-text {
  font-size: 12px;
  margin-bottom: 6px;
  opacity: 0.8;
}

.timer-countdown {
  font-size: 20px;
  font-weight: bold;
  color: #ffeb3b;
  margin-bottom: 4px;
}

.collapse-icon {
  position: absolute;
  top: 4px;
  right: 6px;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.floating-timer:hover .collapse-icon {
  opacity: 1;
}

/* 收起状态 */
.floating-timer.collapsed {
  padding: 6px;
  min-width: 32px;
  min-height: 32px;
  border-radius: 50%;
}

.timer-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.timer-collapsed .collapse-icon {
  font-size: 14px;
  opacity: 0.9;
  position: static;
}

/* 动画效果 */
.floating-timer.collapsed .timer-collapsed {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-timer {
    right: 15px;
    top: 80%;
  }

  .timer-content {
    padding: 10px 12px;
  }

  .timer-countdown {
    font-size: 18px;
  }

  .floating-timer.collapsed {
    min-width: 40px;
    min-height: 40px;
  }
}
</style>
