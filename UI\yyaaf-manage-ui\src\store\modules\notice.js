import { Notification } from 'element-ui'
import { systemNoticeApi } from '@/api'
import { getToken } from '@/utils/auth' // get token from cookie

const state = {
  notifications: [],
  alerts: [],
  intervalId: null, // 记录定时器 ID
  unReadCount: 0, // 消息未读数
  shownNotificationIds: new Set(), // 记录已显示的通知 ID
}

const mutations = {
  SET_NOTIFICATIONS(state, notifications) {
    state.notifications = notifications
  },
  SET_UNREAD_COUNT(state, count) {
    state.unReadCount = count
  },
  SET_ALERTS(state, alerts) {
    state.alerts = alerts
  },
  SET_INTERVAL_ID(state, id) {
    if (!state.intervalId) {
      state.intervalId = id
    }
  },
  CLEAR_INTERVAL(state) {
    if (state.intervalId) {
      clearInterval(state.intervalId)
      state.intervalId = null
    }
  },
  ADD_SHOWN_NOTIFICATION(state, id) {
    state.shownNotificationIds.add(id)
  },
  REMOVE_SHOWN_NOTIFICATION(state, id) {
    state.shownNotificationIds.delete(id)
  },
  CLEAR_SHOWN_NOTIFICATIONS(state) {
    state.shownNotificationIds.clear()
  },
}

const actions = {
  async fetchNotifications({ state, commit }) {
    try {
      const response = await systemNoticeApi.getNoticeInfo()
      if (response.code !== 0) return

      const notifications = response.data.noticeList
      const unReadCount = response.data.unReadCount
      const alerts = response.data.alertList

      notifications.forEach((notification) => {
        // 检查通知 ID 是否已经显示过，防止重复弹出
        if (!state.shownNotificationIds.has(notification.id)) {
          Notification({
            title: notification.title,
            message: notification.description,
            type: notification.type || 'info',
            duration: 0, // 不自动关闭
            position: 'bottom-right',
            dangerouslyUseHTMLString: true,
            onClose: () => {
              systemNoticeApi.readNotice(notification.id, 2)
              commit('REMOVE_SHOWN_NOTIFICATION', notification.id) // 关闭时移除记录
            },
            onClick: () => {
              systemNoticeApi.readNotice(notification.id, 2)
              commit('REMOVE_SHOWN_NOTIFICATION', notification.id) // 点击时移除记录
            },
          })

          commit('ADD_SHOWN_NOTIFICATION', notification.id) // 记录已显示的通知
        }
      })

      commit('SET_NOTIFICATIONS', notifications)
      commit('SET_ALERTS', alerts)
      commit('SET_UNREAD_COUNT', unReadCount)
    } catch (error) {
      console.error('获取通知失败', error)
    }
  },

  startPolling({ state, commit, dispatch }) {
    if (state.intervalId) return

    const hasToken = getToken()
    if (hasToken) {
      dispatch('fetchNotifications')

      const intervalId = setInterval(() => {
        dispatch('fetchNotifications')
      }, 60000) // 每 60 秒查询一次

      commit('SET_INTERVAL_ID', intervalId)
    }
  },

  stopPolling({ commit }) {
    commit('CLEAR_INTERVAL')
    commit('CLEAR_SHOWN_NOTIFICATIONS') // 停止轮询时清除已显示的通知记录
  },
}

export default {
  state,
  mutations,
  actions,
}
