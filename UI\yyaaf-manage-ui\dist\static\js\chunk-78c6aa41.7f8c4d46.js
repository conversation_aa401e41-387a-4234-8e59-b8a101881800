(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-78c6aa41","chunk-4c73d0be","chunk-75fd001a","chunk-091d31a2","chunk-7bcacc10","chunk-2d0bdf53"],{"00d8":function(e,t,n){"use strict";n("6776")},"0316":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:add"],expression:"['deliverTask:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:finish"],expression:"['deliverTask:finish']"}],attrs:{size:"mini",type:"warning",icon:"el-icon-finished"},on:{click:e.handleFinishTask}},[e._v("手动结单")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"任务名称/地区",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("task-info",{attrs:{taskId:t.id,taskName:t.name,areaName:t.areaName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"单价",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.price)+" 元")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"target",label:"目标/进线/重复",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v("\n          "+e._s(n.target)+" / "+e._s(n.trueNewFans)+" / "+e._s(n.trueRepeatFans)+"\n        ")]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"channelCount",label:"渠道数",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"accountCount",label:"主号数",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"上次完成/上次结单",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.lastFinishTime?n("span",[e._v(e._s(a.lastFinishTime))]):n("span",[e._v("未完成")]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.lastStatementTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"enable",label:"是否启用",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-color":"#13ce66"},on:{change:function(t){return e.onChangeEnable(a)}},model:{value:a.enable,callback:function(t){e.$set(a,"enable",t)},expression:"row.enable"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:edit"],expression:"['deliverTask:edit']"}],attrs:{size:"mini"},on:{click:function(n){return e.handleAccounts(t.row)}}},[e._v("管理")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:copy"],expression:"['deliverTask:copy']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleCopy(t.row)}}},[e._v("复制")]),e._v(" "),n("br"),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:edit"],expression:"['deliverTask:edit']"}],staticStyle:{"margin-top":"5px"},attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTask:delete"],expression:"['deliverTask:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)},r=[],i=(n("7f7f"),n("96cf"),n("1da1")),s=n("809c"),o=n("4624"),l=n("365c"),c={components:{TaskInfo:s["a"]},mixins:[o["a"]],data:function(){return{taskIds:[],taskNames:[],queryParams:{},loading:!1,tableData:{}}},created:function(){},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,l["j"].getList(t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$router.push({path:"/deliver/delivertaskadd"})},handleEdit:function(e){this.$router.push({path:"/deliver/delivertaskadd",query:{id:e.id,operation:1}})},handleCopy:function(e){this.$router.push({path:"/deliver/delivertaskadd",query:{id:e.id,operation:9}})},handleAccounts:function(e){this.$router.push({path:"/deliver/deliverchannelaccount",query:{taskid:e.id,taskTitle:e.name}})},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,l["j"].delDeliverTask(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),onChangeEnable:function(e){var t=this,n=1==e.enable?"启用":"禁用";this.$confirm("是否确认【".concat(n,"】任务状态？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function n(){var a;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.$xloading.show(),n.next=3,l["j"].changeEnable(e.id,e.enable);case 3:a=n.sent,t.$xloading.hide(),0==a.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+a.msg),t.$refs.table.refresh();case 7:case"end":return n.stop()}}),n)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()}))},handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.taskIds=e.map((function(e){return e.id})),this.taskNames=e.map((function(e){return e.name}))},handleFinishTask:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.taskIds.length>0?this.$confirm("是否确认对这批任务进行结单？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$xloading.show(),n=0;case 2:if(!(n<t.taskIds.length)){e.next=17;break}return e.next=5,l["j"].finishTask(t.taskIds[n],!1);case 5:if(a=e.sent,t.$xloading.hide(),0!=a.code){e.next=11;break}t.$xMsgSuccess("任务【".concat(t.taskNames[n],"】操作成功")),e.next=14;break;case 11:return t.$xMsgError("任务【".concat(t.taskNames[n],"】操作失败！")+a.msg),e.next=14,t.handleFroceFinishTask(t.taskIds[n],t.taskNames[n],a.msg);case 14:n++,e.next=2;break;case 17:t.$refs.table.refresh();case 18:case"end":return e.stop()}}),e)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()})):this.$xMsgWarning("请选择要手动结单的任务！");case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleFroceFinishTask:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,n,a){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("任务【".concat(n,"】结单错误：").concat(a,", 是否确认进行强制结单？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r.$xloading.show(),e.next=3,l["j"].finishTask(t,!0);case 3:n=e.sent,r.$xloading.hide(),0==n.code?r.$xMsgSuccess("任务【".concat(t,"】操作成功")):r.$xMsgError("任务【".concat(t,"】操作失败！")+n.msg),r.$refs.table.refresh();case 7:case"end":return e.stop()}}),e)})))).catch((function(){r.$xloading.hide(),r.$refs.table.refresh()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t,n,a){return e.apply(this,arguments)}return t}()}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"07b4":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"类别"}},[n("x-select",{attrs:{"show-default":"",url:"/category/options"},model:{value:e.queryParams.cId,callback:function(t){e.$set(e.queryParams,"cId",t)},expression:"queryParams.cId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["template:add"],expression:"['template:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"模板名称/标识",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(n){return e.handlePreview(t.row)}}},[n("div",[e._v(e._s(t.row.name))])]),e._v(" "),n("br"),e._v(" "),n("div",[e._v(e._s(t.row.tempId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"cName",label:"模板类别",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"editTime",label:"编辑时间",align:"center","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"230",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["template:preview"],expression:"['template:preview']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handlePreview(t.row)}}},[e._v("预览")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["template:edit"],expression:"['template:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["template:delete"],expression:"['template:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("49f3"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["G"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$router.push({path:"/page/templateadd"})},handleEdit:function(e){this.$router.push({path:"/page/templateadd",query:{id:e.id}})},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["G"].delTemplate(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handlePreview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,o["G"].preview(t.id);case 3:n=e.sent,a=n,r=window.open(),r.document.open(),r.document.write(a),r.document.close(),this.$xloading.hide();case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},1049:function(e,t,n){"use strict";n("2d73")},"108a":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"渠道标识"}},[n("el-input",{model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"渠道名称"}},[n("el-input",{model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannel:add"],expression:"['deliverChannel:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"渠道标识",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"渠道名称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"添加时间",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannel:edit"],expression:"['deliverChannel:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannel:delete"],expression:"['deliverChannel:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("bf9e"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["f"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["f"].delDeliverChannel(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"10da":function(e,t,n){},"13f9":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"1000px"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入通知标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"通知类型",prop:"type"}},[n("x-radio",{attrs:{button:"",options:e.typeOptions},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("tinymce",{attrs:{"upload-type":e.uploadType,height:400,fullpage:!1},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("8256"),o=n("365c"),l=n("60fe"),c={components:{Tinymce:s["a"]},data:function(){return{title:"",visible:!1,loading:!1,form:{type:1},rules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}]},typeOptions:[{label:"普通通知",value:1},{label:"警告通知",value:2},{label:"危险通知",value:3}],uploadType:l["b"].systemNotice}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["F"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.visible=!1},reset:function(){this.form={id:void 0,title:void 0,content:"",type:1},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["F"].editSystemNotice(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["F"].addSystemNotice(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},1693:function(e,t,n){},1717:function(e,t,n){"use strict";n("1693")},1725:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverArea:add"],expression:"['deliverArea:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"地区",align:"center","min-width":"250"}}),e._v(" "),n("el-table-column",{attrs:{prop:"timeZone",label:"时区",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"sort",label:"排序",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverArea:edit"],expression:"['deliverArea:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverArea:delete"],expression:"['deliverArea:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("f473"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["e"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["e"].delDeliverArea(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"179f":function(e,t,n){},"1bdd":function(e,t,n){},"1ccc":function(e,t,n){},"1ce2":function(e,t,n){},"1cfa":function(e,t,n){"use strict";n("8895")},"1e4b":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[e.isShowUpgradeInfo?n("div",{staticStyle:{margin:"0 0 20px 0"}},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:24}},[n("el-card",{staticStyle:{"text-align":"center",padding:"20px","border-radius":"10px"},attrs:{shadow:"hover"}},[n("p",{staticStyle:{"font-weight":"bold","font-size":"18px",color:"#409eff"}},[e._v("\n            当前最新插件版本为 "+e._s(e.upgradeInfo.verName)+" ,请更新插件！\n          ")]),e._v(" "),n("p",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("请及时更新以获取最新功能和优化体验。")])])],1)],1)],1):e._e(),e._v(" "),e._m(0),e._v(" "),n("el-row",{attrs:{gutter:20}},e._l(e.list,(function(t,a){return n("el-col",{key:a,attrs:{span:8}},[n("el-card",{staticClass:"card-box"},[n("div",{staticClass:"card-header"},[n("div",[n("span",{staticClass:"channelId"},[e._v("🔹 渠道 ID："+e._s(t.cId))]),e._v(" "),n("span",{staticClass:"subtext"},[e._v("📍 地区："+e._s(t.areaName))]),e._v(" "),n("span",{staticClass:"subtext"},[e._v("📌 任务："+e._s(t.taskName))])]),e._v(" "),n("el-button",{staticClass:"copy-btn",attrs:{type:"primary",plain:"",size:"small"},on:{click:function(n){return e.copyAccounts(t.accountList)}}},[e._v("\n            复制主号\n          ")])],1),e._v(" "),n("div",{staticClass:"account-list"},e._l(t.accountList,(function(t){return n("el-tag",{key:t,staticClass:"account-tag",attrs:{type:"success"}},[e._v("\n            "+e._s(t)+"\n          ")])})),1)])],1)})),1)],1)},r=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"info-box"},[e._v("📢 "),n("span",{staticClass:"info-text"},[e._v("每日投放渠道主号信息")])])}],i=(n("8e6e"),n("ac6a"),n("456d"),n("96cf"),n("1da1")),s=(n("7f7f"),n("ade3")),o=n("365c"),l=n("3fa5"),c=n("2f62"),u=n("a78e"),d=n.n(u);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){Object(s["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={name:"Dashboard",data:function(){return{isShowUpgradeInfo:!1,upgradeInfo:{verName:"1.0.0",verCode:1e3},list:{}}},computed:p({},Object(c["c"])(["name","userId"])),created:function(){var e={userId:this.userId,name:this.name};d.a.set("yyaaf_userdata",e)},mounted:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.listenerMessage(),l["a"]({type:"PluginVersion",data:{}}),e.next=4,this.getChannelUserList();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),beforeDestroy:function(){window.removeEventListener("message",this.handleMessage)},methods:{quickStart:function(e){e.href&&this.$router.push({path:e.href})},listenerMessage:function(){var e=this;window.addEventListener("message",(function(t){if("PluginVersionResponse"!==t.data.type);else{var n=t.data.data;n.verCode<e.upgradeInfo.verCode&&(e.isShowUpgradeInfo=!0)}}),!1)},getChannelUserList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,o["h"].getChannelUserList();case 3:t=e.sent,t&&0==t.code?this.list=t.data:this.$xMsgError(t.msg),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),copyAccounts:function(e){var t=e.join("\n"),n=document.createElement("textarea");n.value=t,document.body.appendChild(n),n.select();try{document.execCommand("copy"),this.$xMsgSuccess("复制成功！")}catch(a){console.error("复制失败:",a),this.$xMsgError("复制失败，请手动复制")}document.body.removeChild(n)}}},h=f,v=(n("ca1e"),n("2877")),g=Object(v["a"])(h,a,r,!1,null,"247d8d10",null);t["default"]=g.exports},"1f2c":function(e,t,n){},"1f34":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{size:"mini",type:"danger",icon:"el-icon-plus"},on:{click:e.handleDeleteAllUserLoginToken}},[e._v("清除所有登录用户Token")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"80"}}),e._v(" "),n("el-table-column",{attrs:{prop:"userName",label:"登录账号",align:"center",width:"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"nickName",label:"姓名",align:"center",width:"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"authUId",label:"授权中心ID",align:"center",width:"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{disabled:!e.changeStatusPermission,"active-value":!0,"inactive-value":!1,"active-color":"#13ce66"},on:{change:function(n){return e.onStatusChange(t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{prop:"loginIp",label:"最后登录IP",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(e._s(t.row.loginIp))]),e._v(" "),n("div",[e._v(e._s(t.row.loginIpLocation))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"loginTime",label:"最后登录时间",align:"center",width:"180"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center",width:"180"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding",fixed:"right",width:"350"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:resetPassword"],expression:"['system:user:resetPassword']"}],attrs:{size:"mini"},on:{click:function(n){return e.handleResetPassword(t.row)}}},[e._v("重置密码")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:dataPermission"],expression:"['system:user:dataPermission']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleDataPermission(t.row)}}},[e._v("数据权限")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:delete"],expression:"['system:user:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}}),e._v(" "),n("data-permission-dialog",{ref:"dataPermissionDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("bb53"),c=n("3e29"),u={mixins:[s["a"]],components:{EditDialog:l["default"],DataPermissionDialog:c["default"]},data:function(){return{queryParams:{},loading:!1,tableData:{},changeStatusPermission:!1}},mounted:function(){var e=this.$hasPermission("system:user:changeStatus");this.changeStatusPermission=e},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["E"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDataPermission:function(e){this.$refs.dataPermissionDialog.open(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["E"].deleteUser(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDeleteAllUserLoginToken:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除清除用户登录Token？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$xloading.show(),e.next=3,o["E"].deleteAllUserLoginToken();case 3:n=e.sent,t.$xloading.hide(),0==n.code?(t.$refs.table.refresh(),t.$xMsgSuccess("删除成功")):t.$xMsgError("删除失败！"+n.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){t.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleResetPassword:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认重置该用户的密码","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["E"].resetPassword(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("重置密码成功")):n.$xMsgError("操作失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},onStatusChange:function(e){var t=this,n=1==e.status?"启用":"禁用";this.$confirm("是否确认【".concat(n,"】该账号？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function n(){var a;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.$xloading.show(),n.next=3,o["E"].changeStatus(e.id,e.status);case 3:a=n.sent,t.$xloading.hide(),0==a.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+a.msg),t.$refs.table.refresh();case 7:case"end":return n.stop()}}),n)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()}))}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},2:function(e,t){},"202d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"标题"}},[n("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"发布人"}},[n("x-select",{attrs:{url:"/system/user/options"},model:{value:e.queryParams.publisher,callback:function(t){e.$set(e.queryParams,"publisher",t)},expression:"queryParams.publisher"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"发布时间"}},[n("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.createOnTimePickerOptions},model:{value:e.queryParams.createOn,callback:function(t){e.$set(e.queryParams,"createOn",t)},expression:"queryParams.createOn"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:add"],expression:"['systemNotice:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:detail"],expression:"['systemNotice:detail']"}],attrs:{size:"mini",type:"info"},on:{click:e.handleReadAll}},[e._v("一键已读")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight},on:{"sort-change":e.handleSortChange}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.isRead?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("已读")]):n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("未读")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"title",label:"标题",align:"center","min-width":"350"}}),e._v(" "),n("el-table-column",{attrs:{prop:"publisherName",label:"发布管理员",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"发布时间",align:"center","min-width":"180",sortable:"custom"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:detail"],expression:"['systemNotice:detail']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleRead(t.row)}}},[e._v("已读")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:edit"],expression:"['systemNotice:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:del"],expression:"['systemNotice:del']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("4624"),l=n("365c"),c=n("13f9"),u={components:{EditDialog:c["default"]},mixins:[o["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},createOnTimePickerOptions:s["a"]}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.createOn&&(t.beginTime=t.createOn[0],t.endTime=t.createOn[1],delete t.createOn),e.next=5,l["F"].getList(t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleRead:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["F"].readNotice(t.snuId,1);case 3:n=e.sent,this.$xloading.hide(),0==n.code?(this.$refs.table.refresh(),this.$xMsgSuccess("操作成功")):this.$xMsgError("操作失败！"+n.msg);case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleReadAll:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["F"].readAll();case 3:t=e.sent,this.$xloading.hide(),0==t.code?(this.$refs.table.refresh(),this.$xMsgSuccess("操作成功"),window.location.reload()):this.$xMsgError("操作失败！"+t.msg);case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,l["F"].delSystemNotice(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSortChange:function(e){var t=e.order,n=e.prop;n&&(this.queryParams.field=n,this.queryParams.order="ascending"==t?"asc":"desc",this.$refs.table.refresh(!0))}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},2141:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入工单标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"工单Url",prop:"url"}},[n("el-input",{attrs:{placeholder:"请输入工单Url"},model:{value:e.form.url,callback:function(t){e.$set(e.form,"url",t)},expression:"form.url"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工单账号",prop:"account"}},[n("el-input",{attrs:{placeholder:"请输入工单账号，非必填"},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工单密码",prop:"passWord"}},[n("el-input",{attrs:{placeholder:"请输入工单密码"},model:{value:e.form.passWord,callback:function(t){e.$set(e.form,"passWord",t)},expression:"form.passWord"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"UUId",prop:"uuId"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.uuId,callback:function(t){e.$set(e.form,"uuId",t)},expression:"form.uuId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"SId",prop:"sId"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.sId,callback:function(t){e.$set(e.form,"sId",t)},expression:"form.sId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"WorkId",prop:"workId"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.workId,callback:function(t){e.$set(e.form,"workId",t)},expression:"form.workId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工单号",prop:"orderId"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.orderId,callback:function(t){e.$set(e.form,"orderId",t)},expression:"form.orderId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工单过期时间",prop:"expireTime"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.expireTime,callback:function(t){e.$set(e.form,"expireTime",t)},expression:"form.expireTime"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工单状态",prop:"state"}},[n("x-select",{attrs:{url:"/options/getCounterOrderConfigStateTypes"},model:{value:e.form.state,callback:function(t){e.$set(e.form,"state",t)},expression:"form.state"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"平台",prop:"platform"}},[n("x-radio",{attrs:{button:"",options:e.platformOptions},model:{value:e.form.platform,callback:function(t){e.$set(e.form,"platform",t)},expression:"form.platform"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"抓取用户",prop:"filterAccounts"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:5},placeholder:"请输入需要抓取的用户,不填写全抓，格式：accountId换行accountId2换行accountId3"},model:{value:e.form.filterAccounts,callback:function(t){e.$set(e.form,"filterAccounts",t)},expression:"form.filterAccounts"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{enable:!0,state:0,passWord:""},rules:{title:[{required:!0,message:"请输入工单标题",trigger:"blur"}],url:[{required:!0,message:"请输入工单链接",trigger:"blur"}],passWord:[{required:!0,message:"请输入工单密码",trigger:"blur"}],platform:[{required:!0,message:"选择工单平台",trigger:"blur"}]},platformOptions:[{label:"IMX",value:"IMX"},{label:"SCRM",value:"SCRM"},{label:"SIHAI",value:"SIHAI"}],enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["c"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={enable:!0,state:0,passWord:""},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["c"].editCounterOrderConfig(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["c"].addCounterOrderConfig(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},2360:function(e,t,n){"use strict";n.r(t);var a,r,i=n("2877"),s={},o=Object(i["a"])(s,a,r,!1,null,null,null);t["default"]=o.exports},"23c5":function(e,t,n){"use strict";n("ecd0")},2490:function(e,t,n){},"25ed":function(e,t,n){},"26fc":function(e,t,n){e.exports=n.p+"static/img/404_cloud.0f4bc32b.png"},2828:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"抓取URL",prop:"originalUrl"}},[n("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入抓取链接"},model:{value:e.form.originalUrl,callback:function(t){e.$set(e.form,"originalUrl",t)},expression:"form.originalUrl"}}),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.spiderPage}},[e._v("抓取")])],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"模板标识",prop:"tempId"}},[n("el-input",{attrs:{placeholder:"请输入模板标识，抓取文章等情况下不要修改"},model:{value:e.form.tempId,callback:function(t){e.$set(e.form,"tempId",t)},expression:"form.tempId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入模板名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"类别",prop:"cid"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{url:"/category/options"},model:{value:e.form.cId,callback:function(t){e.$set(e.form,"cId",t)},expression:"form.cId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("el-card",{staticStyle:{"border-radius":"8px",padding:"10px"},attrs:{shadow:"never"}},[n("div",{staticClass:"editor-header"},[n("span",{staticClass:"editor-mode"},[e._v(e._s(e.showSourceCode?"源码模式":"编辑模式"))]),e._v(" "),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.toggleSourceMode}},[e._v("\n                  "+e._s(e.showSourceCode?"切换到编辑模式":"切换到源码模式")+"\n                ")])],1),e._v(" "),e.showSourceCode?n("textarea",{directives:[{name:"model",rawName:"v-model",value:e.form.content,expression:"form.content"}],staticClass:"source-code-textarea",domProps:{value:e.form.content},on:{input:function(t){t.target.composing||e.$set(e.form,"content",t.target.value)}}}):n("tinymce",{attrs:{"upload-temp":e.uploadTemp,uploadTempId:e.uploadTempId},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1)],1)],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("提交")])],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("8256"),l=n("365c"),c=n("ed08"),u={components:{Tinymce:o["a"]},data:function(){return{title:"新增模板",form:{enable:!0,tempId:Object(c["b"])(32)},rules:{name:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}],tempId:[{required:!0,message:"请输入模板标识",trigger:"blur"}]},uploadTemp:"preview",enableOptions:s["b"],showSourceCode:!1}},created:function(){var e=this.$route.query.id;e&&this.edit({id:e})},computed:{uploadTempId:function(){return this.form.tempId}},methods:{edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.title="修改模板",this.$xloading.show(),e.next=4,l["G"].getDetail(t.id);case 4:n=e.sent,0==n.code?(this.form=n.data,this.uploadTemp="release"):this.$xMsgError("加载失败！"+n.msg),this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={name:void 0,content:"",enable:!0,tempId:Object(c["b"])(32)},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=16;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,!r){t.next=10;break}return t.next=7,l["G"].editTemplate(a);case 7:t.t0=t.sent,t.next=13;break;case 10:return t.next=12,l["G"].addTemplate(a);case 12:t.t0=t.sent;case 13:i=t.t0,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess(r?"修改成功":"添加成功"),e.close()):e.$xMsgError("操作失败！"+i.msg);case 16:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},spiderPage:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},this.form),e.next=4,l["G"].spiderTemplate(t);case 4:n=e.sent,n&&0==n.code?(this.form.content=n.data.content,this.uploadTemp="preview"):this.$xMsgError("抓取失败！"+n.msg),this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),toggleSourceMode:function(){this.showSourceCode=!this.showSourceCode},close:function(){this.$router.push({path:"/page/template"})}}},d=u,m=(n("f7b9"),n("2877")),p=Object(m["a"])(d,a,r,!1,null,"4f023e06",null);t["default"]=p.exports},"283f":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"date",label:"日期",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"host",label:"域名",align:"center","min-width":"250"}}),e._v(" "),n("el-table-column",{attrs:{prop:"fBclid",label:"FBclid",align:"center","min-width":"250"}}),e._v(" "),n("el-table-column",{attrs:{prop:"ip",label:"IP",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"count",label:"访问量",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"status",label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.status?n("el-tag",{attrs:{type:"success"}},[e._v("正常")]):1==a.status?n("el-tag",{attrs:{type:"danger"}},[e._v("异常")]):n("el-tag",{attrs:{type:"danger"}},[e._v("黑名单")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["pagehostdate:addblack"],expression:"['pagehostdate:addblack']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleAddToBlackList(t.row)}}},[e._v("添加黑名单")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["pagehostdate:removeblack"],expression:"['pagehostdate:removeblack']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleRemoveFromBlackList(t.row)}}},[e._v("移除黑名单")])]}}])})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["B"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAddToBlackList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认添加【".concat(t.host,"】到黑名单？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["B"].addToBlackList(t);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("操作成功")):n.$xMsgError("操作失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleRemoveFromBlackList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认从黑名单中移除【".concat(t.host,"】？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["B"].removeBlackList(t);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("操作成功")):n.$xMsgError("操作失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},"2c57":function(e,t,n){},"2d73":function(e,t,n){},"2de0":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container",attrs:{align:"center"}},[n("h2",[e._v(e._s(e.text))])])},r=[],i=(n("8e6e"),n("ac6a"),n("456d"),n("96cf"),n("1da1")),s=n("ade3"),o=(n("a481"),n("2f62"));function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){Object(s["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={data:function(){return{text:"登录中...",loading:!1}},mounted:function(){var e=this.$router.history.current.query;if(!e.code)return this.$xMsgError("无效的code，请重试"),void this.$router.replace({path:"/login"});this.getToken(e)},methods:c(c({},Object(o["b"])(["OAuthLogin"])),{},{getToken:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.prev=1,e.next=4,this.OAuthLogin(t).then((function(e){n.$router.replace({path:"/"})}));case 4:this.$xloading.hide(),e.next=12;break;case 7:e.prev=7,e.t0=e["catch"](1),this.$xMsgError("登录失败，"+e.t0),this.$router.replace({path:"/login"}),this.$xloading.hide();case 12:case"end":return e.stop()}}),e,this,[[1,7]])})));function t(t){return e.apply(this,arguments)}return t}()})},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},"2e0d":function(e,t,n){},"2e5c":function(e,t,n){"use strict";n("2e0d")},"2f4d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("iframe",{staticClass:"iframe-preview",attrs:{srcdoc:e.htmlContent}})])},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{htmlContent:""}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.$router.history.current.query,!t.id){e.next=4;break}return e.next=4,this.preview(t.id);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{preview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,s["G"].preview(t);case 3:n=e.sent,0==n.code&&(this.htmlContent=n.data.content),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},l=o,c=(n("7093"),n("2877")),u=Object(c["a"])(l,a,r,!1,null,"67afa104",null);t["default"]=u.exports},"32d1":function(e,t,n){},3347:function(e,t,n){},3620:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return l})),n.d(t,"d",(function(){return c})),n.d(t,"a",(function(){return d}));n("96cf"),n("6b54");var a=n("1da1"),r=n("365c");n("60fe");function i(){return s.apply(this,arguments)}function s(){return s=Object(a["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r["f"].getChannelOptions();case 2:return t=e.sent,e.abrupt("return",o(t));case 4:case"end":return e.stop()}}),e)}))),s.apply(this,arguments)}function o(e){return 0==e.code?e.data:[]}var l=[{label:"是",value:!0},{label:"否",value:!1}],c=[{label:"UTC (GMT) - 格林威治（英国），冰岛，西非部分地区",value:0},{label:"UTC+1 - 西欧（西班牙、法国、德国）、比利时、阿尔及利亚",value:1},{label:"UTC+2 - 南非、希腊、以色列、埃及",value:2},{label:"UTC+3 - 俄罗斯莫斯科、沙特阿拉伯、坦桑尼亚",value:3},{label:"UTC+4 - 阿联酋、阿塞拜疆、亚美尼亚",value:4},{label:"UTC+5 - 哈萨克斯坦、乌兹别克斯坦",value:5},{label:"UTC+6 - 孟加拉国、吉尔吉斯斯坦",value:6},{label:"UTC+7 - 泰国、越南、柬埔寨",value:7},{label:"UTC+8 - 中国、新加坡、马来西亚、澳大利亚西部",value:8},{label:"UTC+9 - 日本、韩国",value:9},{label:"UTC+10 - 澳大利亚东部（悉尼、墨尔本）",value:10},{label:"UTC-1 - 亚速尔群岛（葡萄牙），佛得角岛",value:-1},{label:"UTC-2 - 南乔治亚岛（南大西洋）",value:-2},{label:"UTC-3 - 阿根廷、巴西、乌拉圭",value:-3},{label:"UTC-4 - 加拿大、玻利维亚、委内瑞拉",value:-4},{label:"UTC-5 - 美国东部（纽约、华盛顿）",value:-5},{label:"UTC-6 - 美国中部（芝加哥、墨西哥城）",value:-6},{label:"UTC-7 - 美国山地时间（丹佛）",value:-7},{label:"UTC-8 - 美国西部（洛杉矶、旧金山）",value:-8},{label:"UTC-9 - 阿拉斯加",value:-9},{label:"UTC-10 - 夏威夷",value:-10},{label:"UTC-11 - 美属萨摩亚岛",value:-11},{label:"UTC-12 - 贝克岛、豪兰岛（无人岛）",value:-12}],u=864e5,d={shortcuts:[{text:"昨日",onClick:function(e){var t=new Date,n=new Date;t.setTime(t.getTime()-u),n=t,e.$emit("pick",[t,n])}},{text:"今日",onClick:function(e){var t=new Date,n=new Date;e.$emit("pick",[t,n])}},{text:"最近三日",onClick:function(e){var t=new Date,n=new Date;t.setTime(t.getTime()-2*u),e.$emit("pick",[t,n])}},{text:"最近一周",onClick:function(e){var t=new Date,n=new Date;t.setTime(t.getTime()-6*u),e.$emit("pick",[t,n])}},{text:"最近一个月",onClick:function(e){var t=new Date,n=new Date;t.setTime(t.getTime()-29*u),e.$emit("pick",[t,n])}}]}},3743:function(e,t,n){"use strict";n("1ce2")},"3a7b":function(e,t,n){},"3ce1":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("p",{staticStyle:{margin:"0 0 16px 0"}},[n("strong",[e._v("任务名称：")]),e._v(e._s(e.taskTitle))]),e._v(" "),n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"渠道标识"}},[n("el-input",{model:{value:e.queryParams.cId,callback:function(t){e.$set(e.queryParams,"cId",t)},expression:"queryParams.cId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"主号"}},[n("el-input",{model:{value:e.queryParams.account,callback:function(t){e.$set(e.queryParams,"account",t)},expression:"queryParams.account"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"状态"}},[n("x-select",{attrs:{options:e.enableOptions},model:{value:e.queryParams.isEnable,callback:function(t){e.$set(e.queryParams,"isEnable",t)},expression:"queryParams.isEnable"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTaskChannelAccount:add"],expression:"['deliverTaskChannelAccount:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-document-add"},on:{click:e.handleAdd}},[e._v("加号")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTaskChannelAccount:edit"],expression:"['deliverTaskChannelAccount:edit']"}],attrs:{size:"mini",type:"warning",icon:"el-icon-edit"},on:{click:e.handleChangeFans}},[e._v("调粉")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverTaskChannelAccount:change"],expression:"['deliverTaskChannelAccount:change']"}],attrs:{size:"mini",type:"warning",icon:"el-icon-edit"},on:{click:e.handleBetchChangeAccount}},[e._v("批量换号")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"account",label:"主号",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleGotoCounter(a.account)}}},[n("div",[e._v(e._s(a.account))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"table-cell-content"},[n("span",{staticClass:"sc-id"},[e._v(e._s(a.cId))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"sc-name"},[e._v(e._s(a.cName))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"target",label:"目标/进线/重复",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v("\n          "+e._s(n.target)+" / "+e._s(n.newFans)+" / "+e._s(n.repeatFans)+"\n        ")]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"target",label:"进线率",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.newFansPer)+"% ")]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"target",label:"IP/UV/PV",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handlePageCollectRecord(a)}}},[n("span",[e._v(e._s(a.ip)+" / "+e._s(a.uv)+" / "+e._s(a.pv))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"权重",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[[n("el-input-number",{attrs:{size:"small",placeholder:"请输入数字(限数字)"},on:{change:function(n){return e.onWeightChange(t.row)}},model:{value:t.row.weight,callback:function(n){e.$set(t.row,"weight",e._n(n))},expression:"scope.row.weight"}})]]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"强制进线",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-color":"#13ce66"},on:{change:function(n){return e.onForceInChange(t.row)}},model:{value:t.row.forceIn,callback:function(n){e.$set(t.row,"forceIn",n)},expression:"scope.row.forceIn"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"强制下线",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-color":"#13ce66"},on:{change:function(n){return e.onForceOffChange(t.row)}},model:{value:t.row.forceOff,callback:function(n){e.$set(t.row,"forceOff",n)},expression:"scope.row.forceOff"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.isEnable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("上线")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("下线")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"原因",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.disableReason?n("div",[1==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"success",size:"medium"}},[e._v("调粉")]):2==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"danger",size:"medium"}},[e._v("封禁")]):3==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"info",size:"medium"}},[e._v("任务")]):5==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"warning",size:"medium"}},[e._v("未找到")]):6==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"warning",size:"medium"}},[e._v("强制下线")]):7==a.disableReason?n("el-tag",{attrs:{effect:"plain",type:"warning",size:"medium"}},[e._v("换号")]):n("el-tag",{attrs:{effect:"plain",type:"info",size:"medium"}},[e._v("弃用")])],1):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"changeTime",label:"修改时间",sortable:"",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center",width:"120",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleChangeAccount(t.row)}}},[e._v("换号")])]}}])})],1)],1),e._v(" "),n("change-fans-dialog",{ref:"changeFansDialog",on:{ok:e.handleOk}}),e._v(" "),n("add-dialog",{ref:"addDialog",on:{ok:e.handleOk}}),e._v(" "),n("change-account-dialog",{ref:"changeAccountDialog",on:{ok:e.handleOk}}),e._v(" "),n("betch-change-account",{ref:"betchChangeAccount",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("b943"),c=n("51be"),u=n("fb9d"),d=n("de28"),m={components:{ChangeFansDialog:l["default"],AddDialog:c["default"],ChangeAccountDialog:u["default"],BetchChangeAccount:d["default"]},mixins:[s["a"]],data:function(){return{taskid:-1,taskTitle:"",accounts:[],queryParams:{isEnable:null},loading:!1,tableData:{},enableOptions:[{label:"全部",value:null},{label:"启用",value:!0},{label:"禁用",value:!1}]}},created:function(){var e=this.$router.history.current.query;e.taskid?(this.taskid=e.taskid,this.queryParams.taskid=this.taskid,this.taskTitle=e.taskTitle):this.$xMsgWarning("需要携带任务Id参数！")},methods:{queryReset:function(){this.queryParams={isEnable:null},this.queryParams.taskid=this.taskid,this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["l"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$refs.addDialog.add(this.queryParams.taskid);case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleChangeFans:function(){this.accounts.length>0?this.$refs.changeFansDialog.edit(this.accounts,this.queryParams.taskid):this.$xMsgWarning("请选择要调粉的记录！")},handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.accounts=e.map((function(e){return e.account}))},handleChangeAccount:function(e){this.$refs.changeAccountDialog.open(e)},handleBetchChangeAccount:function(){var e={taskId:this.taskid,taskTitle:this.taskTitle};this.$refs.betchChangeAccount.open(e)},onForceInChange:function(e){var t=this,n=1==e.forceIn?"启用":"禁用";this.$confirm("是否确认【".concat(n,"】强制进线状态？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function n(){var a;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.$xloading.show(),n.next=3,o["l"].changeForceIn(e.id,e.forceIn);case 3:a=n.sent,t.$xloading.hide(),0==a.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+a.msg),t.$refs.table.refresh();case 7:case"end":return n.stop()}}),n)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()}))},onForceOffChange:function(e){var t=this,n=1==e.forceOff?"启用":"禁用";this.$confirm("是否确认【".concat(n,"】强制下线状态？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function n(){var a;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.$xloading.show(),n.next=3,o["l"].changeForceOff(e.id,e.forceOff);case 3:a=n.sent,t.$xloading.hide(),0==a.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+a.msg),t.$refs.table.refresh();case 7:case"end":return n.stop()}}),n)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()}))},onWeightChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,o["l"].changeWeight(t.id,t.weight);case 3:n=e.sent,this.$xloading.hide(),0==n.code?this.$xMsgSuccess("操作成功"):this.$xMsgError("操作失败！"+n.msg),this.$refs.table.refresh();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleGotoCounter:function(e){window.open("/counter/counterorderdaterecords?account=".concat(e),"_blank")},handlePageCollectRecord:function(e){this.$router.push({path:"/page/pagecollectrecord",query:{date:e.date,type:2,areaId:e.areaId,taskConfigId:e.taskConfigId,cid:e.cId,account:e.account}})}}},p=m,f=(n("669e"),n("2877")),h=Object(f["a"])(p,a,r,!1,null,"33a5d5a4",null);t["default"]=h.exports},"3e29":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"800px"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px",size:"medium"}},[n("el-form-item",{attrs:{label:"用户信息"}},[n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v(e._s(e.userInfo))])],1),e._v(" "),n("el-divider",{attrs:{"content-position":"left"}},[e._v("数据权限配置")]),e._v(" "),n("el-form-item",{attrs:{label:"任务权限"}},[n("div",{staticClass:"permission-section"},[n("div",{staticClass:"section-header"},[n("el-checkbox",{attrs:{indeterminate:e.taskIndeterminate},on:{change:e.handleTaskAllChange},model:{value:e.taskAllSelected,callback:function(t){e.taskAllSelected=t},expression:"taskAllSelected"}},[e._v("\n            全选任务\n          ")]),e._v(" "),n("span",{staticClass:"selected-count"},[e._v("(已选择 "+e._s(e.selectedTasks.length)+" 项)")])],1),e._v(" "),n("div",{staticClass:"options-container"},[n("el-checkbox-group",{on:{change:e.handleTaskChange},model:{value:e.selectedTasks,callback:function(t){e.selectedTasks=t},expression:"selectedTasks"}},e._l(e.taskOptions,(function(t){return n("el-checkbox",{key:t.value,staticClass:"option-item",attrs:{label:t.value}},[e._v("\n              "+e._s(t.label)+"\n            ")])})),1)],1)])]),e._v(" "),n("el-form-item",{attrs:{label:"计数器权限"}},[n("div",{staticClass:"permission-section"},[n("div",{staticClass:"section-header"},[n("el-checkbox",{attrs:{indeterminate:e.counterIndeterminate},on:{change:e.handleCounterAllChange},model:{value:e.counterAllSelected,callback:function(t){e.counterAllSelected=t},expression:"counterAllSelected"}},[e._v("\n            全选计数器\n          ")]),e._v(" "),n("span",{staticClass:"selected-count"},[e._v("(已选择 "+e._s(e.selectedCounters.length)+" 项)")])],1),e._v(" "),n("div",{staticClass:"options-container"},[n("el-checkbox-group",{on:{change:e.handleCounterChange},model:{value:e.selectedCounters,callback:function(t){e.selectedCounters=t},expression:"selectedCounters"}},e._l(e.counterOptions,(function(t){return n("el-checkbox",{key:t.value,staticClass:"option-item",attrs:{label:t.value}},[e._v("\n              "+e._s(t.label)+"\n            ")])})),1)],1)])])],1)],1)},r=[],i=n("2909"),s=(n("96cf"),n("1da1")),o=n("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{userId:null,permissions:[]},currentUser:null,taskOptions:[],selectedTasks:[],taskAllSelected:!1,taskIndeterminate:!1,counterOptions:[],selectedCounters:[],counterAllSelected:!1,counterIndeterminate:!1}},computed:{userInfo:function(){return this.currentUser?"".concat(this.currentUser.nickName," (").concat(this.currentUser.userName,")"):""}},methods:{open:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.currentUser=t,this.form.userId=t.id,this.title="数据权限配置 - ".concat(t.nickName),this.visible=!0,e.next=6,this.loadOptions();case 6:return e.next=8,this.loadUserPermissions();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),loadOptions:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,e.next=4,o["H"].getTaskConfigOptions();case 4:return t=e.sent,0===t.code&&(this.taskOptions=t.data||[]),e.next=8,o["H"].getCounterOrderConfigOptions();case 8:n=e.sent,0===n.code&&(this.counterOptions=n.data||[]),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](0),this.$xMsgError("加载选项数据失败："+e.t0.message);case 15:return e.prev=15,this.loading=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,this,[[0,12,15,18]])})));function t(){return e.apply(this,arguments)}return t}(),loadUserPermissions:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,e.next=4,o["H"].getUserDataPermissions(this.form.userId);case 4:t=e.sent,0===t.code&&(n=t.data.permissions||[],this.selectedTasks=n.filter((function(e){return"DeliverTaskConfig"===e.resourceType})).map((function(e){return e.resourceId})),this.selectedCounters=n.filter((function(e){return"CounterOrderConfig"===e.resourceType})).map((function(e){return e.resourceId})),this.updateTaskSelectState(),this.updateCounterSelectState()),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$xMsgError("加载用户权限失败："+e.t0.message);case 11:return e.prev=11,this.loading=!1,e.finish(11);case 14:case"end":return e.stop()}}),e,this,[[0,8,11,14]])})));function t(){return e.apply(this,arguments)}return t}(),handleTaskAllChange:function(e){this.selectedTasks=e?this.taskOptions.map((function(e){return e.value})):[],this.taskIndeterminate=!1},handleTaskChange:function(){this.updateTaskSelectState()},updateTaskSelectState:function(){var e=this.selectedTasks.length;this.taskAllSelected=e===this.taskOptions.length,this.taskIndeterminate=e>0&&e<this.taskOptions.length},handleCounterAllChange:function(e){this.selectedCounters=e?this.counterOptions.map((function(e){return e.value})):[],this.counterIndeterminate=!1},handleCounterChange:function(){this.updateCounterSelectState()},updateCounterSelectState:function(){var e=this.selectedCounters.length;this.counterAllSelected=e===this.counterOptions.length,this.counterIndeterminate=e>0&&e<this.counterOptions.length},submitForm:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,t=[].concat(Object(i["a"])(this.selectedTasks.map((function(e){return{resourceType:"DeliverTaskConfig",resourceId:e}}))),Object(i["a"])(this.selectedCounters.map((function(e){return{resourceType:"CounterOrderConfig",resourceId:e}})))),n={userId:this.form.userId,permissions:t},e.next=6,o["H"].saveUserDataPermissions(n);case 6:a=e.sent,0===a.code?(this.$xMsgSuccess("数据权限配置成功"),this.visible=!1,this.$emit("ok")):this.$xMsgError("配置失败："+a.msg),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),this.$xMsgError("配置失败："+e.t0.message);case 13:return e.prev=13,this.loading=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[0,10,13,16]])})));function t(){return e.apply(this,arguments)}return t}(),reset:function(){this.form={userId:null,permissions:[]},this.currentUser=null,this.selectedTasks=[],this.selectedCounters=[],this.taskAllSelected=!1,this.taskIndeterminate=!1,this.counterAllSelected=!1,this.counterIndeterminate=!1}}},c=l,u=(n("7d12"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,"6d67f466",null);t["default"]=d.exports},"3fa5":function(e,t,n){"use strict";function a(e){window.parent.postMessage(e,"*")}n.d(t,"a",(function(){return a}))},"41e8":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[n("div",{staticClass:"page-title"},[e._v("修改密码")]),e._v(" "),n("el-row",[n("el-col",{attrs:{span:24,offset:2}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"100px",inline:"",size:"small"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"旧密码",prop:"oldPassword"}},[n("el-input",{attrs:{placeholder:"","show-password":"",autocomplete:"new-password"},model:{value:e.form.oldPassword,callback:function(t){e.$set(e.form,"oldPassword","string"===typeof t?t.trim():t)},expression:"form.oldPassword"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"新密码",prop:"password"}},[n("el-input",{attrs:{placeholder:"","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password","string"===typeof t?t.trim():t)},expression:"form.password"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"确认新密码",prop:"confirmPassword"}},[n("el-input",{attrs:{placeholder:"","show-password":""},model:{value:e.form.confirmPassword,callback:function(t){e.$set(e.form,"confirmPassword","string"===typeof t?t.trim():t)},expression:"form.confirmPassword"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:8,offset:2}},[n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),e._v(" "),n("el-button",{on:{click:e.resetForm}},[e._v("重置")])],1)],1)],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("7d92"),o=n("365c"),l={data:function(){var e=this,t=function(t,n,a){""===n?a(new Error("请再次输入密码")):n!==e.form.password?a(new Error("两次输入密码不一致!")):a()};return{loading:!1,form:{},formRules:{oldPassword:[{required:!0,message:"不能为空",trigger:"blur"}],password:[{required:!0,message:"不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符"}],confirmPassword:[{required:!0,message:"不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符"},{validator:t}]}}},methods:{submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=20;break}return e.$xloading.show(),a=Object.assign({},e.form),a.oldPassword=Object(s["c"])(a.oldPassword),a.password=Object(s["c"])(a.password),a.confirmPassword=Object(s["c"])(a.confirmPassword),t.next=8,o["E"].changePassword(a);case 8:if(r=t.sent,e.$xloading.hide(),0!=r.code){t.next=19;break}return e.$xMsgSuccess("修改成功"),e.visible=!1,e.resetForm(),t.next=16,e.$store.dispatch("Logout");case 16:setTimeout((function(){e.$router.push("/login")}),1500),t.next=20;break;case 19:e.$xMsgError("操作失败！"+r.msg);case 20:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},resetForm:function(){this.$xResetForm("form")}}},c=l,u=(n("1717"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,"ad9e835a",null);t["default"]=d.exports},4306:function(e,t,n){"use strict";n("62e9")},4395:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-card",[n("el-tabs",{attrs:{"tab-position":"left"}},[n("el-tab-pane",[n("div",{staticClass:"tab-title",attrs:{slot:"label"},slot:"label"},[e._v("修改密码")]),e._v(" "),n("password")],1)],1)],1)],1)],1)],1)},r=[],i=n("41e8"),s={components:{Password:i["default"]}},o=s,l=(n("4306"),n("2877")),c=Object(l["a"])(o,a,r,!1,null,"44d2dc26",null);t["default"]=c.exports},4624:function(e,t,n){"use strict";var a=n("d0a2");n.d(t,"a",(function(){return a["a"]}));n("de11")},4771:function(e,t,n){"use strict";n("10da")},"488d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"广告组ID"}},[n("el-input",{model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"广告账户ID"}},[n("el-input",{model:{value:e.queryParams.account_id,callback:function(t){e.$set(e.queryParams,"account_id",t)},expression:"queryParams.account_id"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"primary"},on:{click:e.handleActiveAd}},[e._v("开启广告组")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"warning"},on:{click:e.handlePausedAd}},[e._v("暂停广告组")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":{fontSize:"13px"}},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{label:"广告组名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.name,secondText:t.id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告系列名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.campaign_name,secondText:t.campaign_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账户名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.account_name,secondText:t.account_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"bid_amount_cal",label:"出价金额",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"每日预算/剩余预算",align:"center","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.daily_budget))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.budget_remaining))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"超出预算",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.recurring_budget_semantics?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("允许")]):n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("不允许")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"effective_status",label:"生产状态",align:"center","min-width":"130"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["ACTIVE"==a.status?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):"PAUSED"==a.status?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("暂停")]):"DELETED"==a.status?n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("删除")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("归档")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"开始时间/结束时间/更新时间",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(e.formattedDate(a.start_time)))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(e.formattedDate(a.end_time)))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.updated_time))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"lastUpdateTime",label:"最后抓取时间",align:"center","min-width":"160"}})],1)],1)],1)},r=[],i=(n("ac6a"),n("96cf"),n("1da1")),s=n("5a0c"),o=n.n(s),l=n("4624"),c=n("365c"),u=n("7c49"),d={components:{TwoRowColumn:u["a"]},mixins:[l["a"]],data:function(){return{selections:[],queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,c["s"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,c["s"].delFBAdSet(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.selections=e.map((function(e){return{id:e.id,account_id:e.account_id}}))},handleActiveAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认开启广告组？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"ACTIVE"},t.next=4,c["s"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")},handlePausedAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认暂停广告组？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"PAUSED"},t.next=4,c["s"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")},formattedDate:function(e){return e?o()(e).format("YYYY-MM-DD HH:mm:ss"):"none"}}},m=d,p=n("2877"),f=Object(p["a"])(m,a,r,!1,null,null,null);t["default"]=f.exports},4938:function(e,t,n){},"499e":function(e,t,n){"use strict";n("e264")},"49d4":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:"Id自动生成"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"域名",prop:"domain"}},[n("el-input",{attrs:{placeholder:"请输入域名"},model:{value:e.form.domain,callback:function(t){e.$set(e.form,"domain",t)},expression:"form.domain"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:16}},[n("el-form-item",{attrs:{label:"权重",prop:"weight"}},[n("el-input-number",{attrs:{placeholder:"请输入权重"},model:{value:e.form.weight,callback:function(t){e.$set(e.form,"weight",t)},expression:"form.weight"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"状态",prop:"state"}},[n("x-radio",{attrs:{button:"",options:e.stateOptions},model:{value:e.form.state,callback:function(t){e.$set(e.form,"state",t)},expression:"form.state"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"随机二级",prop:"isRandomSLD"}},[n("x-radio",{attrs:{button:"",options:e.randomSLDOptions},model:{value:e.form.isRandomSLD,callback:function(t){e.$set(e.form,"isRandomSLD",t)},expression:"form.isRandomSLD"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{title:"",visible:!1,loading:!1,form:{weight:10,state:1},stateOptions:[{label:"上线",value:1},{label:"下线",value:0},{label:"禁用",value:-1}],randomSLDOptions:[{label:"随机",value:!0},{label:"固定",value:!1}]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["n"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={weight:10,state:1},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,s["n"].editDomain(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["n"].addDomain(a);case 14:o=t.sent,e.$xloading.hide(),0==o.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+o.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},l=o,c=n("2877"),u=Object(c["a"])(l,a,r,!1,null,null,null);t["default"]=u.exports},"49f3":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"60%"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"抓取URL",prop:"originalUrl"}},[n("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入抓取链接"},model:{value:e.form.originalUrl,callback:function(t){e.$set(e.form,"originalUrl",t)},expression:"form.originalUrl"}}),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.spiderPage}},[e._v("抓取")])],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入模板名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"类别",prop:"cid"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{url:"/category/options"},model:{value:e.form.cId,callback:function(t){e.$set(e.form,"cId",t)},expression:"form.cId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("el-card",{staticStyle:{"border-radius":"8px",padding:"10px"},attrs:{shadow:"never"}},[n("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center","margin-bottom":"10px"}},[n("span",{staticStyle:{"font-weight":"bold","font-size":"14px"}},[e._v(e._s(e.showSourceCode?"源码模式":"编辑模式"))]),e._v(" "),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.toggleSourceMode}},[e._v("\n                "+e._s(e.showSourceCode?"切换到编辑模式":"切换到源码模式")+"\n              ")])],1),e._v(" "),e.showSourceCode?n("textarea",{directives:[{name:"model",rawName:"v-model",value:e.form.content,expression:"form.content"}],staticClass:"source-code-textarea",domProps:{value:e.form.content},on:{input:function(t){t.target.composing||e.$set(e.form,"content",t.target.value)}}}):n("tinymce",{attrs:{"upload-temp":e.uploadTemp,uploadTempId:e.uploadTempId},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("8256"),l=n("365c"),c=n("ed08"),u={components:{Tinymce:o["a"]},data:function(){return{title:"",visible:!1,loading:!1,form:{enable:!0,tempId:Object(c["b"])(32)},rules:{name:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}]},uploadTemp:"preview",uploadTempId:void 0,enableOptions:s["b"],showSourceCode:!1}},created:function(){this.uploadTempId=this.form.tempId},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,l["G"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data,this.uploadTempId=n.data.tempId,this.uploadTemp="release"),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.visible=!1},reset:function(){this.form={name:void 0,content:"",enable:!0,tempId:Object(c["b"])(32)},this.uploadTempId=this.form.tempId,this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,l["G"].editTemplate(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,l["G"].addTemplate(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},spiderPage:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},this.form),e.next=4,l["G"].spiderTemplate(t);case 4:n=e.sent,n&&0==n.code?(this.form.content=n.data.content,this.uploadTemp="preview"):this.$xMsgError("抓取失败！"+n.msg),this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),toggleSourceMode:function(){this.showSourceCode=!this.showSourceCode}}},d=u,m=(n("fcb2"),n("2877")),p=Object(m["a"])(d,a,r,!1,null,"7dd2d30f",null);t["default"]=p.exports},"4b3b":function(e,t,n){var a={"./":"1e4b","./404":"8cdb","./404.vue":"8cdb","./auth/yyoauth":"2de0","./auth/yyoauth.vue":"2de0","./chromeplugin":"f86d","./chromeplugin/":"f86d","./chromeplugin/index":"f86d","./chromeplugin/index.vue":"f86d","./counter/counterorderconfig":"eb93","./counter/counterorderconfig/":"eb93","./counter/counterorderconfig/batch":"83d6a","./counter/counterorderconfig/batch.vue":"83d6a","./counter/counterorderconfig/edit":"2141","./counter/counterorderconfig/edit.vue":"2141","./counter/counterorderconfig/index":"eb93","./counter/counterorderconfig/index.vue":"eb93","./counter/counterorderdaterecords":"e985","./counter/counterorderdaterecords/":"e985","./counter/counterorderdaterecords/index":"e985","./counter/counterorderdaterecords/index.vue":"e985","./deliver/area":"1725","./deliver/area/":"1725","./deliver/area/edit":"f473","./deliver/area/edit.vue":"f473","./deliver/area/index":"1725","./deliver/area/index.vue":"1725","./deliver/changefans":"4d5d","./deliver/changefans/":"4d5d","./deliver/changefans/index":"4d5d","./deliver/changefans/index.vue":"4d5d","./deliver/channel":"108a","./deliver/channel/":"108a","./deliver/channel/edit":"bf9e","./deliver/channel/edit.vue":"bf9e","./deliver/channel/index":"108a","./deliver/channel/index.vue":"108a","./deliver/channelaccount":"3ce1","./deliver/channelaccount/":"3ce1","./deliver/channelaccount/add":"51be","./deliver/channelaccount/add.vue":"51be","./deliver/channelaccount/betchchangeaccount":"de28","./deliver/channelaccount/betchchangeaccount.vue":"de28","./deliver/channelaccount/changeaccount":"fb9d","./deliver/channelaccount/changeaccount.vue":"fb9d","./deliver/channelaccount/changefans":"b943","./deliver/channelaccount/changefans.vue":"b943","./deliver/channelaccount/index":"3ce1","./deliver/channelaccount/index.vue":"3ce1","./deliver/channeluser":"d4be","./deliver/channeluser/":"d4be","./deliver/channeluser/edit":"70dd","./deliver/channeluser/edit.vue":"70dd","./deliver/channeluser/index":"d4be","./deliver/channeluser/index.vue":"d4be","./deliver/infansspeed":"5f14","./deliver/infansspeed/":"5f14","./deliver/infansspeed/index":"5f14","./deliver/infansspeed/index.vue":"5f14","./deliver/stats/actualtaskstats":"f709","./deliver/stats/actualtaskstats.vue":"f709","./deliver/stats/areastats":"c961","./deliver/stats/areastats.vue":"c961","./deliver/stats/channelstats":"a7c9","./deliver/stats/channelstats.vue":"a7c9","./deliver/stats/pagechanneldatesummary":"5719","./deliver/stats/pagechanneldatesummary.vue":"5719","./deliver/stats/pagetaskdatesummary":"b97c","./deliver/stats/pagetaskdatesummary.vue":"b97c","./deliver/stats/taskstats":"f58d","./deliver/stats/taskstats.vue":"f58d","./deliver/task":"0316","./deliver/task/":"0316","./deliver/task/add":"e722","./deliver/task/add.vue":"e722","./deliver/task/index":"0316","./deliver/task/index.vue":"0316","./domain/domain":"4bb4","./domain/domain/":"4bb4","./domain/domain/edit":"49d4","./domain/domain/edit.vue":"49d4","./domain/domain/index":"4bb4","./domain/domain/index.vue":"4bb4","./domain/domainblacklist":"bbf2","./domain/domainblacklist/":"bbf2","./domain/domainblacklist/edit":"62ec","./domain/domainblacklist/edit.vue":"62ec","./domain/domainblacklist/index":"bbf2","./domain/domainblacklist/index.vue":"bbf2","./fb/fbad":"9fad","./fb/fbad/":"9fad","./fb/fbad/index":"9fad","./fb/fbad/index.vue":"9fad","./fb/fbadaccount":"f051","./fb/fbadaccount/":"f051","./fb/fbadaccount/index":"f051","./fb/fbadaccount/index.vue":"f051","./fb/fbadcampaign":"a1db","./fb/fbadcampaign/":"a1db","./fb/fbadcampaign/index":"a1db","./fb/fbadcampaign/index.vue":"a1db","./fb/fbadset":"488d","./fb/fbadset/":"488d","./fb/fbadset/index":"488d","./fb/fbadset/index.vue":"488d","./fb/fbbusiness":"fde5","./fb/fbbusiness/":"fde5","./fb/fbbusiness/index":"fde5","./fb/fbbusiness/index.vue":"fde5","./fb/fbuser":"f1de","./fb/fbuser/":"f1de","./fb/fbuser/index":"f1de","./fb/fbuser/index.vue":"f1de","./form":"2360","./form.vue":"2360","./index":"1e4b","./index.vue":"1e4b","./login":"9ed6","./login/":"9ed6","./login/index":"9ed6","./login/index.vue":"9ed6","./page/category":"b803","./page/category/":"b803","./page/category/edit":"74f3","./page/category/edit.vue":"74f3","./page/category/index":"b803","./page/category/index.vue":"b803","./page/page":"6bac","./page/page/":"6bac","./page/page/edit":"ccce","./page/page/edit.vue":"ccce","./page/page/index":"6bac","./page/page/index.vue":"6bac","./page/page/preview":"5001","./page/page/preview.vue":"5001","./page/pagecollectrecord":"aa34","./page/pagecollectrecord/":"aa34","./page/pagecollectrecord/index":"aa34","./page/pagecollectrecord/index.vue":"aa34","./page/pagehostdate":"283f","./page/pagehostdate/":"283f","./page/pagehostdate/index":"283f","./page/pagehostdate/index.vue":"283f","./page/redirect":"5eab","./page/redirect/":"5eab","./page/redirect/edit":"81db","./page/redirect/edit.vue":"81db","./page/redirect/index":"5eab","./page/redirect/index.vue":"5eab","./page/template":"07b4","./page/template/":"07b4","./page/template/add":"2828","./page/template/add.vue":"2828","./page/template/edit":"49f3","./page/template/edit.vue":"49f3","./page/template/index":"07b4","./page/template/index.vue":"07b4","./page/template/preview":"2f4d","./page/template/preview.vue":"2f4d","./stats/adinsights":"96ca","./stats/adinsights/":"96ca","./stats/adinsights/index":"96ca","./stats/adinsights/index.vue":"96ca","./system/account-setting":"4395","./system/account-setting/":"4395","./system/account-setting/index":"4395","./system/account-setting/index.vue":"4395","./system/account-setting/password":"41e8","./system/account-setting/password/":"41e8","./system/account-setting/password/index":"41e8","./system/account-setting/password/index.vue":"41e8","./system/log/login":"f5e8","./system/log/login/":"f5e8","./system/log/login/index":"f5e8","./system/log/login/index.vue":"f5e8","./system/log/oper":"e38b","./system/log/oper/":"e38b","./system/log/oper/DetailDialog":"55cb","./system/log/oper/DetailDialog.vue":"55cb","./system/log/oper/index":"e38b","./system/log/oper/index.vue":"e38b","./system/menu":"f794","./system/menu/":"f794","./system/menu/Edit":"89ee","./system/menu/Edit.vue":"89ee","./system/menu/index":"f794","./system/menu/index.vue":"f794","./system/notice":"202d","./system/notice/":"202d","./system/notice/Edit":"13f9","./system/notice/Edit.vue":"13f9","./system/notice/index":"202d","./system/notice/index.vue":"202d","./system/role":"70eb","./system/role/":"70eb","./system/role/Edit":"c641","./system/role/Edit.vue":"c641","./system/role/index":"70eb","./system/role/index.vue":"70eb","./system/user":"1f34","./system/user/":"1f34","./system/user/DataPermission":"3e29","./system/user/DataPermission.vue":"3e29","./system/user/Edit":"bb53","./system/user/Edit.vue":"bb53","./system/user/index":"1f34","./system/user/index.vue":"1f34","./tools/gen":"837b","./tools/gen/":"837b","./tools/gen/PreviewDialog":"99b0","./tools/gen/PreviewDialog.vue":"99b0","./tools/gen/index":"837b","./tools/gen/index.vue":"837b","./tools/redis":"f56f","./tools/redis/":"f56f","./tools/redis/index":"f56f","./tools/redis/index.vue":"f56f"};function r(e){var t=i(e);return n(t)}function i(e){var t=a[e];if(!(t+1)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return t}r.keys=function(){return Object.keys(a)},r.resolve=i,e.exports=r,r.id="4b3b"},"4bb4":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"id"}},[n("el-input",{model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"域名"}},[n("el-input",{model:{value:e.queryParams.domain,callback:function(t){e.$set(e.queryParams,"domain",t)},expression:"queryParams.domain"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"状态"}},[n("x-select",{attrs:{"show-default":"",options:e.stateOptions},model:{value:e.queryParams.state,callback:function(t){e.$set(e.queryParams,"state",t)},expression:"queryParams.state"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domain:add"],expression:"['domain:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"domain",label:"域名",align:"center","min-width":"250"}}),e._v(" "),n("el-table-column",{attrs:{prop:"weight",label:"权重",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"isRandomSLD",label:"随机域名",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[1==a.isRandomSLD?n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v("随机")]):0==a.isRandomSLD?n("el-tag",{attrs:{type:"info",size:"medium"}},[e._v("固定")]):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"state",label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[1==a.state?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("上线")]):0==a.state?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("下线")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"添加/修改时间",align:"center","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.createOn))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.editTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domain:edit"],expression:"['domain:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domain:delete"],expression:"['domain:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("49d4"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{state:null},loading:!1,tableData:{},stateOptions:[{label:"全部",value:null},{label:"上线",value:1},{label:"下线",value:0},{label:"禁用",value:-1}]}},methods:{queryReset:function(){this.queryParams={state:null},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["n"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["n"].delDomain(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"4d27":function(e,t,n){},"4d5d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},on:{change:e.fetchTaskConfigOption},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"任务"}},[n("x-select",{attrs:{"show-default":"",options:e.taskConfigOptions},model:{value:e.queryParams.taskConfigId,callback:function(t){e.$set(e.queryParams,"taskConfigId",t)},expression:"queryParams.taskConfigId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"主号"}},[n("el-input",{model:{value:e.queryParams.account,callback:function(t){e.$set(e.queryParams,"account",t)},expression:"queryParams.account"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"类型"}},[n("x-select",{attrs:{"show-default":"",options:e.typeOptions},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"调粉时段"}},[n("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"taskName",label:"任务名称/地区",align:"center","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("task-info",{attrs:{taskId:t.taskId,taskName:t.taskName,areaName:t.areaName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"account",label:"主号",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleGotoCounter(a.account)}}},[n("div",[e._v(e._s(a.account))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"类型",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.type?n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v("调粉")]):1==a.type?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("封号")]):3==a.type?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("未找到")]):4==a.type?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("强制下线")]):5==a.type?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("换号")]):n("el-tag",{attrs:{type:"info",size:"medium"}},[e._v("恢复")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"调粉渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.scId,cName:t.scName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"目标渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.tcId,cName:t.tcName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"newFans",label:"新粉数量",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"repeatFans",label:"重粉数量",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"price",label:"价格",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"changeTime",label:"调粉时间",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"adminName",label:"管理员",align:"center","min-width":"100"}})],1)],1),e._v(" "),n("countdown",{ref:"timer",attrs:{table:e.$refs.table}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("5a0c"),o=n.n(s),l=n("8046"),c=n("809c"),u=n("9abd"),d=n("4624"),m=n("365c"),p={components:{TaskInfo:c["a"],ChannelInfo:u["a"],Countdown:l["a"]},mixins:[d["a"]],data:function(){return{queryParams:{type:-1},loading:!1,tableData:{},typeOptions:[{label:"调粉",value:0},{label:"封号",value:1},{label:"恢复",value:2},{label:"未找到",value:3},{label:"强制下线",value:4},{label:"换号",value:5}],taskConfigOptions:[]}},created:function(){},beforeRouteLeave:function(e,t,n){this.$refs.timer.stopAutoRefresh(),n()},methods:{queryReset:function(){this.queryParams={type:-1},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,m["k"].getList(t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),fetchTaskConfigOption:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,m["y"].getTaskConfigOptions({areaId:this.queryParams.areaId});case 2:t=e.sent,t&&0==t.code?this.taskConfigOptions=t.data:this.$xMsgError(t.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),reset:function(){var e=o()().format("YYYY-MM-DD 00:00:00"),t=o()().add(1,"day").format("YYYY-MM-DD 00:00:00");this.queryParams.date=[e,t]},handleGotoCounter:function(e){window.open("/counter/counterorderdaterecords?account=".concat(e),"_blank")}}},f=p,h=n("2877"),v=Object(h["a"])(f,a,r,!1,null,null,null);t["default"]=v.exports},5001:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("iframe",{staticClass:"iframe-preview",attrs:{srcdoc:e.htmlContent}})])},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{htmlContent:""}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.$router.history.current.query,!t.id){e.next=4;break}return e.next=4,this.preview(t.id);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{preview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,s["z"].preview(t);case 3:n=e.sent,0==n.code&&(this.htmlContent=n.data.content),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},l=o,c=(n("c0c7"),n("2877")),u=Object(c["a"])(l,a,r,!1,null,"1bbba573",null);t["default"]=u.exports},"51be":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:16}},[n("el-form-item",{attrs:{label:"加号渠道标识",prop:"cid"}},[n("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"fetch-suggestions":e.querySearch,placeholder:"请输入加号的目标渠道标识","trigger-on-focus":!1,debounce:50,"prefix-icon":"el-icon-search"},on:{select:e.handleSelect},model:{value:e.form.cid,callback:function(t){e.$set(e.form,"cid",t)},expression:"form.cid"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"主号列表",prop:"accounts"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:8},placeholder:"请输入主号，多个换行分隔"},model:{value:e.formattedAccounts,callback:function(t){e.formattedAccounts=t},expression:"formattedAccounts"}})],1)],1)],1)],1)],1)},r=[],i=(n("8e6e"),n("ac6a"),n("456d"),n("7f7f"),n("ade3")),s=(n("28a5"),n("96cf"),n("1da1")),o=n("3620"),l=n("365c");function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){Object(i["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d={components:{},data:function(){return{ids:[],title:"",visible:!1,loading:!1,accounts:[],form:{accounts:[]},channelOptions:[]}},created:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["c"])();case 2:this.channelOptions=e.sent;case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),computed:{formattedAccounts:{get:function(){return this.accounts.join("\n")},set:function(e){this.accounts=e.split("\n")}}},methods:{add:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.reset(),this.title="加号操作",this.visible=!0,this.form.taskid=t;case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(n){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=4;break}return a=Object.assign({},e.form,{accounts:e.accounts}),t.next=4,e.handleAdd(a);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleAdd:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认进行加号操作？","提示",{type:"warning"}).then(Object(s["a"])(regeneratorRuntime.mark((function e(){var a,r,i,s,o,c,d,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.$xloading.show(),a=t.accounts||[],r=0,i=0,s=[],o=0;case 6:if(!(o<a.length)){e.next=25;break}if(c=a[o],c&&""!==c.trim()){e.next=10;break}return e.abrupt("continue",22);case 10:return e.prev=10,d=u(u({},t),{},{account:c.trim()}),e.next=14,l["l"].add(d);case 14:m=e.sent,0==m.code?r++:(i++,s.push("".concat(c,": ").concat(m.msg)),n.$xMsgError(m.msg)),e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](10),i++,s.push("".concat(c,": ").concat(e.t0.message||"网络错误"));case 22:o++,e.next=6;break;case 25:n.$xloading.hide(),0===i?(n.$xMsgSuccess("操作成功！共处理 ".concat(r," 个账号")),n.visible=!1,n.$emit("ok")):n.$emit("ok");case 27:case"end":return e.stop()}}),e,null,[[10,18]])})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),querySearch:function(e,t){var n=this.channelOptions,a=e?n.filter((function(t){return t.value.toLowerCase().indexOf(e.toLowerCase())>-1||t.label.toLowerCase().indexOf(e.toLowerCase())>-1})):n;a=a.map((function(e){return{value:e.label+" 【"+e.value+"】",name:e.value}})),t(a)},handleSelect:function(e){this.form.cid=e.name}}},m=d,p=n("2877"),f=Object(p["a"])(m,a,r,!1,null,null,null);t["default"]=f.exports},"55cb":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"900px"},on:{"update:visible":function(t){e.visible=t},close:e.onClose}},[n("el-divider",{attrs:{"content-position":"left"}},[e._v(e._s(e.subTitle))]),e._v(" "),n("el-row",{attrs:{gutter:20}},[e.oldValue?n("el-col",{attrs:{span:12}},[n("div",{staticClass:"waring"},[n("json-viewer",{attrs:{value:e.oldValue,boxed:"",copyable:""}})],1)]):e._e(),e._v(" "),e.newValue?n("el-col",{attrs:{span:12}},[n("div",{staticClass:"success"},[n("json-viewer",{attrs:{value:e.newValue,boxed:"",copyable:""}})],1)]):e._e(),e._v(" "),e.desc?n("el-col",{attrs:{span:24}},[n("div",{staticClass:"waring"},[n("json-viewer",{attrs:{value:e.desc,boxed:"",copyable:""}})],1)]):e._e()],1),e._v(" "),e.isFailure?[n("el-divider",{attrs:{"content-position":"left"}},[e._v("异常信息")]),e._v(" "),e.desc?n("el-col",{attrs:{span:24}},[n("div",{staticClass:"waring"},[n("pre",[e._v("          异常信息："+e._s(e.exception.msg)+"\n          异常类型："+e._s(e.exception.type)+"\n          异常堆栈："+e._s(e.exception.stackTrace)+"\n        ")])])]):e._e()]:e._e(),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")])],1)],2)},r=[],i=n("60fe"),s=n("349e"),o=n.n(s),l={components:{JsonViewer:o.a},data:function(){return{title:"详情",visible:!1,loading:!1,subTitle:"",oldValue:"",newValue:"",isFailure:!1,desc:"",exception:{}}},methods:{show:function(e){if(this.visible=!0,"0"!=e.status)return this.isFailure=!0,this.subTitle="请求数据",e.desc&&(this.desc=JSON.parse(e.desc)),this.exception.msg=e.exceptionMsg,this.exception.type=e.exceptionType,void(this.exception.stackTrace=e.exceptionStackTrace);e.operateType==i["OperateType"].Add?(this.subTitle="新增的数据",this.newValue=JSON.parse(e.newValue)):e.operateType==i["OperateType"].Edit?(this.subTitle="修改的数据",e.oldValue&&(this.oldValue=JSON.parse(e.oldValue)),this.newValue=JSON.parse(e.newValue)):(this.subTitle="详情",this.subTitle="无")},onClose:function(){this.oldValue=void 0,this.newValue=void 0,this.desc=void 0,this.isFailure=!1}}},c=l,u=(n("8490"),n("8f91"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,"8823c306",null);t["default"]=d.exports},5719:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{page:!1,data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"summary-method":e.getSummaries}},[e._v("\n      >\n      "),n("el-table-column",{attrs:{label:"日期",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"地区",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.areaName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.cId,cName:t.cName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"进线"}},[n("el-table-column",{attrs:{label:"IP/UV/PV",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handlePageCollectRecord(a)}}},[n("span",[e._v(e._s(a.ip)+" / "+e._s(a.uv)+" / "+e._s(a.pv))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"newFans",label:"进线",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"进线率",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.newFansPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"未知"}},[n("el-table-column",{attrs:{prop:"none",label:"未知",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.nonePer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"FB"}},[n("el-table-column",{attrs:{prop:"fb",label:"FB",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.fbPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"INS"}},[n("el-table-column",{attrs:{prop:"ins",label:"INS",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.insPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"Snap"}},[n("el-table-column",{attrs:{prop:"snap",label:"Snap",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.snapPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"KW"}},[n("el-table-column",{attrs:{prop:"kw",label:"KW",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.kwPer)+"%")])]}}])})],1)],1)],1)],1)},r=[],i=(n("c5f6"),n("96cf"),n("1da1")),s=(n("ac6a"),n("5df3"),n("4f7f"),n("9abd")),o=n("4624"),l=n("365c"),c={components:{ChannelInfo:s["a"]},mixins:[o["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{},expandedChildren:new Set}},created:function(){},methods:{queryReset:function(){this.queryParams={date:(new Date).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["i"].getDeliverPageChannelDateSummary(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getSummaries:function(e){var t=e.columns,n=e.data,a=[];return t.forEach((function(e,t){if(0!==t){var r=n.map((function(t){return Number(t[e.property])}));r.every((function(e){return isNaN(e)}))?a[t]="--":a[t]=r.reduce((function(e,t){return e+t}),0)}else a[t]="合计"})),a},handlePageCollectRecord:function(e){this.$router.push({path:"/page/pagecollectrecord",query:{date:e.date,type:2,areaId:e.areaId,cid:e.cId}})}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"5eab":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"渠道"}},[n("x-select",{attrs:{filterable:"",url:"/deliverChannel/options"},model:{value:e.queryParams.cId,callback:function(t){e.$set(e.queryParams,"cId",t)},expression:"queryParams.cId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["pageRedirect:add"],expression:"['pageRedirect:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{label:"页面标识/标题",align:"center","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.id))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.title))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"cateName",label:"地区",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.areaName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.cId,cName:t.cName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"pixelId",label:"像素",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"时间",align:"center","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.createOn))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.editTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"230",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["page:preview"],expression:"['page:preview']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleDeliverRedirectUrl(t.row)}}},[e._v("链接")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["pageRedirect:edit"],expression:"['pageRedirect:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["pageRedirect:delete"],expression:"['pageRedirect:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("9abd"),o=n("4624"),l=n("365c"),c=n("81db"),u={components:{ChannelInfo:s["a"],EditDialog:c["default"]},mixins:[o["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["C"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDeliverRedirectUrl:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["C"].deliverRedirectUrl(t.id);case 3:n=e.sent,0==n.code&&(a=n.data,this.copyAccounts(a)),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,l["C"].delPageRedirect(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),copyAccounts:function(e){var t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select();try{document.execCommand("copy"),this.$xMsgSuccess("复制成功！")}catch(n){this.$xMsgError("复制失败，请手动复制")}document.body.removeChild(t)},handleOk:function(){this.$refs.table.refresh()}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},"5f14":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},on:{change:e.fetchTaskConfigOption},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"任务"}},[n("x-select",{attrs:{"show-default":"",options:e.taskConfigOptions},model:{value:e.queryParams.taskConfigId,callback:function(t){e.$set(e.queryParams,"taskConfigId",t)},expression:"queryParams.taskConfigId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"投放时段"}},[n("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"header-cell-style":{fontSize:"14px"},"cell-style":{fontSize:"14px"},"row-class-name":e.tableRowClassName,border:""}},e._l(e.columns,(function(t,a){return n("el-table-column",{key:a,attrs:{prop:t.prop,label:t.label,align:"center","min-width":t.width},scopedSlots:e._u([{key:"default",fn:function(a){return["taskName"===t.prop?n("task-info",{attrs:{taskId:a.row["taskId"],taskName:a.row[t.prop],areaName:a.row["areaName"]}}):n("span",[e._v("\n            "+e._s(a.row[t.prop]||0)+"\n          ")])]}}],null,!0)})})),1)],1),e._v(" "),n("countdown",{ref:"timer",attrs:{table:e.$refs.table}})],1)},r=[],i=(n("5df3"),n("4f7f"),n("2909")),s=(n("6762"),n("2fdb"),n("ac6a"),n("8615"),n("96cf"),n("1da1")),o=n("5a0c"),l=n.n(o),c=n("8046"),u=n("809c"),d=n("4624"),m=n("365c"),p={components:{TaskInfo:u["a"],Countdown:c["a"]},mixins:[d["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},columns:[],taskNameColors:{},taskConfigOptions:[],colorClasses:["task-color-1","task-color-2","task-color-3","task-color-4","task-color-5","task-color-6","task-color-7","task-color-8","task-color-9","task-color-10"]}},created:function(){},beforeRouteLeave:function(e,t,n){this.$refs.timer.stopAutoRefresh(),n()},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getColumns:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,m["m"].getTaskInFansColumns();case 3:t=e.sent,t&&0==t.code?this.columns=t.data:this.$xMsgError(t.msg),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getList:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,m["m"].getList(t);case 5:n=e.sent,this.columns=n.columns,this.tableData=n,this.assignTaskColors(),this.$xloading.hide();case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),fetchTaskConfigOption:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,m["y"].getTaskConfigOptions({areaId:this.queryParams.areaId});case 2:t=e.sent,t&&0==t.code?this.taskConfigOptions=t.data:this.$xMsgError(t.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().format("YYYY-MM-DD 00:00:00"),t=l()().add(1,"day").format("YYYY-MM-DD 00:00:00");this.queryParams.date=[e,t]},tableRowClassName:function(e){var t=e.row,n=t.taskName;if(!n)return"";if(!this.taskNameColors[n]){var a=Object.values(this.taskNameColors),r=this.colorClasses.filter((function(e){return!a.includes(e)}));if(r.length>0)this.taskNameColors[n]=r[0];else{var i=this.hashCode(n);this.taskNameColors[n]=this.colorClasses[Math.abs(i)%this.colorClasses.length]}}return this.taskNameColors[n]},hashCode:function(e){for(var t=0,n=0;n<e.length;n++){var a=e.charCodeAt(n);t=(t<<5)-t+a,t&=t}return t},assignTaskColors:function(){var e=this;if(this.tableData.data){var t=Object(i["a"])(new Set(this.tableData.data.map((function(e){return e.taskName})).filter((function(e){return e}))));this.taskNameColors={},t.forEach((function(t,n){if(n<e.colorClasses.length)e.taskNameColors[t]=e.colorClasses[n];else{var a=e.hashCode(t);e.taskNameColors[t]=e.colorClasses[Math.abs(a)%e.colorClasses.length]}}))}}}},f=p,h=(n("6b3b"),n("2877")),v=Object(h["a"])(f,a,r,!1,null,null,null);t["default"]=v.exports},"5ffc":function(e,t,n){},"60fe":function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return r}));var a=[{label:"正常",value:1},{label:"异常",value:2},{label:"作弊",value:3},{label:"测试用户",value:99}],r={deliverTemplateImage:"deliverTemplateImage",deliverTemplateCss:"deliverTemplateCss",systemNotice:"systemNotice"}},"62e9":function(e,t,n){},"62ec":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:"Id自动生成"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"域名",prop:"domain"}},[n("el-input",{attrs:{placeholder:"请输入要屏蔽的域名"},model:{value:e.form.domain,callback:function(t){e.$set(e.form,"domain",t)},expression:"form.domain"}}),e._v(" "),n("span",{staticClass:"extra"},[e._v("*使用的是包含判断，请填写完整host，如：www.729183.xyz")])],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{title:"",visible:!1,loading:!1,form:{}}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["o"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,s["o"].editDomainBlackList(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["o"].addDomainBlackList(a);case 14:o=t.sent,e.$xloading.hide(),0==o.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+o.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},l=o,c=n("2877"),u=Object(c["a"])(l,a,r,!1,null,null,null);t["default"]=u.exports},"669e":function(e,t,n){"use strict";n("4938")},6776:function(e,t,n){},"691a":function(e,t,n){},"6b3b":function(e,t,n){"use strict";n("5ffc")},"6b9d":function(e,t,n){"use strict";n("952f")},"6bac":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"类别"}},[n("x-select",{attrs:{filterable:"","show-default":"",url:"/category/options"},model:{value:e.queryParams.cateId,callback:function(t){e.$set(e.queryParams,"cateId",t)},expression:"queryParams.cateId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"渠道"}},[n("x-select",{attrs:{filterable:"",url:"/deliverChannel/options"},model:{value:e.queryParams.cId,callback:function(t){e.$set(e.queryParams,"cId",t)},expression:"queryParams.cId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["page:add"],expression:"['page:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":{fontSize:"13px"}}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"title",label:"页面标题",align:"center","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.title))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"页面标识/模板",align:"center","min-width":"280"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleDeliverUrl(a)}}},[n("span",[e._v(e._s(a.id))])]),e._v(" "),n("br"),e._v(" "),n("el-link",{attrs:{type:"warning",underline:!1},on:{click:function(t){return e.handleTemplatePreview(a)}}},[n("div",[e._v(e._s(a.tName))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"cateName",label:"类别/地区",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.cateName))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.areaName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.cId,cName:t.cName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"pixelId",label:"像素",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"时间",align:"center","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.createOn))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.editTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"230",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["page:preview"],expression:"['page:preview']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleDeliverUrl(t.row)}}},[e._v("链接")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["page:edit"],expression:"['page:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["page:delete"],expression:"['page:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("9abd"),o=n("4624"),l=n("365c"),c=n("ccce"),u={components:{ChannelInfo:s["a"],EditDialog:c["default"]},mixins:[o["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["z"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handlePreview:function(e){window.open("https://customer.729183.xyz/api/page/PreviewPage?id=".concat(e.id))},handleTemplatePreview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["G"].preview(t.tId);case 3:n=e.sent,a=n,r=window.open(),r.document.open(),r.document.write(a),r.document.close(),this.$xloading.hide();case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDeliverUrl:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["z"].deliverUrl(t.id);case 3:n=e.sent,0==n.code&&(a=n.data,window.open(a)),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,l["z"].delPage(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},"6ebb":function(e,t,n){"use strict";n("ea9a")},7093:function(e,t,n){"use strict";n("b6c6")},"70dd":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:"Id自动生成"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"账号",prop:"userName"}},[n("el-input",{attrs:{placeholder:"请输入账号"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"名称",prop:"nickName"}},[n("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),e._v(" "),"新增"==this.title?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"密码",prop:"password"}},[n("el-input",{attrs:{placeholder:"请输入密码","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1)],1):e._e(),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"账号渠道",prop:"channelIdList"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{multiple:"",url:"/deliverChannel/options"},model:{value:e.form.channelIdList,callback:function(t){e.$set(e.form,"channelIdList",t)},expression:"form.channelIdList"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"状态",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l=(n("7d92"),{data:function(){return{title:"",visible:!1,loading:!1,form:{enable:!0,channelIdList:[]},channelOptions:[],enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["g"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={enable:!0,channelIdList:[]},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["g"].editchannelUser(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["g"].addchannelUser(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}}),c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},"70eb":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"80"}}),e._v(" "),n("el-table-column",{attrs:{prop:"roleName",label:"角色名称"}}),e._v(" "),n("el-table-column",{attrs:{prop:"sort",label:"排序",align:"center",width:"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{disabled:!e.changeStatusPermission,"active-value":!0,"inactive-value":!1},on:{change:function(n){return e.onStatusChange(t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center",width:"200"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"180",align:"center","class-name":"small-padding"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:delete"],expression:"['system:role:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("3528"),l=n("c641"),c={mixins:[s["a"]],components:{EditDialog:l["default"]},data:function(){return{queryParams:{},loading:!1,tableData:{},changeStatusPermission:!1}},mounted:function(){this.changeStatusPermission=this.$hasPermission("system:role:changeStatus")},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["f"](t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["c"](t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},onStatusChange:function(e){var t=this,n=0==e.status?"启用":"禁用";this.$confirm("是否确认【".concat(n,"】该角色？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function n(){var a;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,o["b"](e.id,e.status);case 2:a=n.sent,0==a.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+a.msg),t.$refs.table.refresh();case 5:case"end":return n.stop()}}),n)})))).catch((function(){t.$refs.table.refresh()}))}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},"74f3":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"排序",prop:"sort"}},[n("el-input-number",{staticStyle:{width:"40%"},attrs:{placeholder:"请输入排序"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{title:"",visible:!1,loading:!1,form:{sort:1},rules:{name:[{required:!0,message:"请输入类别名称",trigger:"blur"}],sort:[{required:!0,message:"请输入排序",trigger:"blur"}]}}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["b"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={sort:1},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,s["b"].editCategory(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["b"].addCategory(a);case 14:o=t.sent,e.$xloading.hide(),0==o.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+o.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},l=o,c=n("2877"),u=Object(c["a"])(l,a,r,!1,null,null,null);t["default"]=u.exports},"7a5c":function(e,t,n){},"7c49":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("span",[e._v(e._s(e.firstText))]),e._v(" "),n("br"),e._v(" "),n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v(e._s(e.secondText))])],1)},r=[],i={data:function(){return{}},props:["firstText","secondText"],methods:{}},s=i,o=n("2877"),l=Object(o["a"])(s,a,r,!1,null,null,null);t["a"]=l.exports},"7d12":function(e,t,n){"use strict";n("25ed")},"7d92":function(e,t,n){"use strict";n.d(t,"c",(function(){return u})),n.d(t,"b",(function(){return p})),n.d(t,"a",(function(){return f}));n("f576"),n("ed50"),n("6b54");var a=n("3452"),r=n.n(a),i=(n("c198"),n("720d")),s=n.n(i);r.a.mode.CBC,r.a.pad.Pkcs7;var o="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfYk/CK1aLSxfIsQ94dXXsgbXVRLlva\nCdQl3egrNJ1+s0WJqgyPdFDIFw/TiwW6cVtYKN5Tm12rC1aVUhE7Mc0CAwEAAQ==",l="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALkumIqQMhoRbM5opek++pTvyB/VZCtH\nJJqXj0olueiarjwMqFiVgyQjwREddT1y5Sijj1PH9GPLs4GLv9y/y50CAwEAAQ==",c="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAuS6YipAyGhFszmil\n6T76lO/IH9VkK0ckmpePSiW56JquPAyoWJWDJCPBER11PXLlKKOPU8f0Y8uzgYu/\n3L/LnQIDAQABAkBZhV8UzTSLSZUyC4D5SwrUaT5ztTMhgNj/KvmIPMis2xc+1KRS\nKlSSx00+qdm4bKDSB0D80MI5r2FDyov7YUe5AiEA5gT9d+U4bBSguBara4sg62UG\nhp21XPsAdWVhpHg0+rsCIQDOGSF9asLMhNreIwgy88zVp/IkyBf/MGmsZYj82Y0J\nhwIgBOnmYEFNS0HFjSku0EVQlra5xPZpgWr7P4bC5ziKKTECIQDL900QjQbqVxUw\nQGVN38A5NrPKuQgesm/ygK345ujQowIgPNvJ7AO+SS+MPHdnVn9pk7Jwcmlo0aQb\nz85W+KC0dBE=";function u(e){return d(e,o)}function d(e,t){var n=new s.a;return n.setPublicKey(t),n.encrypt(e)}function m(e,t){var n=new s.a;return n.setPrivateKey(t),n.decrypt(e)}function p(e){return d(e,l)}function f(e){return m(e,c)}},"7fe1":function(e,t,n){},8046:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"floating-timer",class:{collapsed:e.isCollapsed,dragging:e.isDragging},style:{top:e.position.y+"px",right:e.position.x+"px"},on:{mousedown:e.startDrag,click:e.handleClick}},[e.isCollapsed?n("div",{staticClass:"timer-collapsed"},[n("i",{staticClass:"el-icon-time collapse-icon",attrs:{title:"展开倒计时"}})]):n("div",{staticClass:"timer-content"},[n("span",{staticClass:"timer-text"},[e._v("下次刷新:")]),e._v(" "),n("span",{staticClass:"timer-countdown"},[e._v(e._s(e.countdown)+"s")]),e._v(" "),n("i",{staticClass:"el-icon-minus collapse-icon",attrs:{title:"收起"}})])])},r=[],i={props:["table"],data:function(){return{countdown:60,refreshTimer:null,isCollapsed:!1,isDragging:!1,position:{x:20,y:.85*window.innerHeight},dragStart:{x:0,y:0},clickTimeout:null}},mounted:function(){this.startAutoRefresh();var e=localStorage.getItem("countdown-collapsed");null!==e&&(this.isCollapsed="true"===e);var t=localStorage.getItem("countdown-position");t&&(this.position=JSON.parse(t)),document.addEventListener("mousemove",this.onMouseMove),document.addEventListener("mouseup",this.onMouseUp),window.addEventListener("resize",this.onWindowResize)},beforeUnmount:function(){clearInterval(this.refreshTimer),document.removeEventListener("mousemove",this.onMouseMove),document.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("resize",this.onWindowResize),this.clickTimeout&&clearTimeout(this.clickTimeout)},methods:{startAutoRefresh:function(){var e=this;this.refreshTimer=setInterval((function(){e.countdown<=0?(e.table&&e.table.refresh(!1),e.countdown=60):e.countdown--}),1e3)},stopAutoRefresh:function(){clearInterval(this.refreshTimer)},toggleCollapse:function(){this.isCollapsed=!this.isCollapsed,localStorage.setItem("countdown-collapsed",this.isCollapsed)},handleClick:function(e){var t=this;this.isDragging||(this.clickTimeout=setTimeout((function(){t.toggleCollapse()}),150))},startDrag:function(e){e.preventDefault(),this.isDragging=!1,this.dragStart={x:e.clientX,y:e.clientY},this.clickTimeout&&(clearTimeout(this.clickTimeout),this.clickTimeout=null)},onMouseMove:function(e){if(0!==this.dragStart.x||0!==this.dragStart.y){var t=e.clientX-this.dragStart.x,n=e.clientY-this.dragStart.y;if(Math.abs(t)>5||Math.abs(n)>5){this.isDragging=!0;var a=this.position.x-t,r=this.position.y+n,i=window.innerWidth-50,s=window.innerHeight-50;this.position.x=Math.max(10,Math.min(i,a)),this.position.y=Math.max(10,Math.min(s,r)),this.dragStart={x:e.clientX,y:e.clientY}}}},onMouseUp:function(){var e=this;this.isDragging&&localStorage.setItem("countdown-position",JSON.stringify(this.position)),setTimeout((function(){e.isDragging=!1}),100),this.dragStart={x:0,y:0}},onWindowResize:function(){var e=window.innerWidth-50,t=window.innerHeight-50;this.position.x=Math.max(10,Math.min(e,this.position.x)),this.position.y=Math.max(10,Math.min(t,this.position.y)),localStorage.setItem("countdown-position",JSON.stringify(this.position))}}},s=i,o=(n("c10a"),n("2877")),l=Object(o["a"])(s,a,r,!1,null,"716f7e70",null);t["a"]=l.exports},"809c":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"table-cell-content"},[n("el-link",{staticClass:"task-id",attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleAccounts()}}},[n("div",[e._v(e._s(e.taskName))])]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"area-name"},[e._v(e._s(e.areaName))])],1)},r=[],i={data:function(){return{}},props:["taskId","taskName","areaName"],methods:{handleAccounts:function(){window.open("/deliver/deliverchannelaccount?taskid=".concat(this.taskId,"&taskTitle=").concat(encodeURIComponent(this.taskName)))}}},s=i,o=(n("6ebb"),n("2877")),l=Object(o["a"])(s,a,r,!1,null,"e57c49d2",null);t["a"]=l.exports},"81db":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"地区",prop:"areaId"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{url:"/deliverArea/options"},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"问候文字",prop:"wsText"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:3},placeholder:"请输入问候携带的文字"},model:{value:e.form.wsText,callback:function(t){e.$set(e.form,"wsText",t)},expression:"form.wsText"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"渠道标识",prop:"cId"}},[n("x-select",{attrs:{filterable:"",url:"/deliverChannel/options"},model:{value:e.form.cId,callback:function(t){e.$set(e.form,"cId",t)},expression:"form.cId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"像素",prop:"pixelId"}},[n("el-input",{attrs:{placeholder:"请输入像素Id"},model:{value:e.form.pixelId,callback:function(t){e.$set(e.form,"pixelId",t)},expression:"form.pixelId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l=n("ed08"),c={data:function(){return{title:"",visible:!1,loading:!1,form:{title:Object(l["b"])(32),enable:!0},templateOptions:[],enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["C"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={title:Object(l["b"])(32),enable:!0},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["C"].editPageRedirect(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["C"].addPageRedirect(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},8206:function(e){e.exports=["platform-eleme","eleme","delete-solid","delete","s-tools","setting","user-solid","user","phone","phone-outline","more","more-outline","star-on","star-off","s-goods","goods","warning","warning-outline","question","info","remove","circle-plus","success","error","zoom-in","zoom-out","remove-outline","circle-plus-outline","circle-check","circle-close","s-help","help","minus","plus","check","close","picture","picture-outline","picture-outline-round","upload","upload2","download","camera-solid","camera","video-camera-solid","video-camera","message-solid","bell","s-cooperation","s-order","s-platform","s-fold","s-unfold","s-operation","s-promotion","s-home","s-release","s-ticket","s-management","s-open","s-shop","s-marketing","s-flag","s-comment","s-finance","s-claim","s-custom","s-opportunity","s-data","s-check","s-grid","menu","share","d-caret","caret-left","caret-right","caret-bottom","caret-top","bottom-left","bottom-right","back","right","bottom","top","top-left","top-right","arrow-left","arrow-right","arrow-down","arrow-up","d-arrow-left","d-arrow-right","video-pause","video-play","refresh","refresh-right","refresh-left","finished","sort","sort-up","sort-down","rank","loading","view","c-scale-to-original","date","edit","edit-outline","folder","folder-opened","folder-add","folder-remove","folder-delete","folder-checked","tickets","document-remove","document-delete","document-copy","document-checked","document","document-add","printer","paperclip","takeaway-box","search","monitor","attract","mobile","scissors","umbrella","headset","brush","mouse","coordinate","magic-stick","reading","data-line","data-board","pie-chart","data-analysis","collection-tag","film","suitcase","suitcase-1","receiving","collection","files","notebook-1","notebook-2","toilet-paper","office-building","school","table-lamp","house","no-smoking","smoking","shopping-cart-full","shopping-cart-1","shopping-cart-2","shopping-bag-1","shopping-bag-2","sold-out","sell","present","box","bank-card","money","coin","wallet","discount","price-tag","news","guide","male","female","thumb","cpu","link","connection","open","turn-off","set-up","chat-round","chat-line-round","chat-square","chat-dot-round","chat-dot-square","chat-line-square","message","postcard","position","turn-off-microphone","microphone","close-notification","bangzhu","time","odometer","crop","aim","switch-button","full-screen","copy-document","mic","stopwatch","medal-1","medal","trophy","trophy-1","first-aid-kit","discover","place","location","location-outline","location-information","add-location","delete-location","map-location","alarm-clock","timer","watch-1","watch","lock","unlock","key","service","mobile-phone","bicycle","truck","ship","basketball","football","soccer","baseball","wind-power","light-rain","lightning","heavy-rain","sunrise","sunrise-1","sunset","sunny","cloudy","partly-cloudy","cloudy-and-sunny","moon","moon-night","dish","dish-1","food","chicken","fork-spoon","knife-fork","burger","tableware","sugar","dessert","ice-cream","hot-water","water-cup","coffee-cup","cold-drink","goblet","goblet-full","goblet-square","goblet-square-full","refrigerator","grape","watermelon","cherry","apple","pear","orange","coffee","ice-tea","ice-drink","milk-tea","potato-strips","lollipop","ice-cream-square","ice-cream-round"]},8256:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("editor",{staticClass:"editor",attrs:{id:"tinymce",init:e.editorInit},model:{value:e.content,callback:function(t){e.content=t},expression:"content"}})},r=[],i=(n("a481"),n("2909")),s=(n("c5f6"),n("e562"),n("9917")),o=(n("0d68"),n("46c3"),n("64d8"),n("4237"),n("d2dc"),n("84ec"),n("07d1"),n("4ea8"),n("c3d7"),n("4bd0"),n("0a9d"),n("e455"),["undo redo | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent |","styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat |","table image charmap hr insertdatetime | preview | uploadCssButton"]),l=o,c=["link","anchor","lists","table","image","charmap","insertdatetime","preview"],u=c,d=n("3f5e"),m=n("5f87"),p={components:{Editor:s["a"]},props:{value:{type:String},plugins:{type:[String,Array],default:function(){return u}},toolbar:{type:[String,Array],default:function(){return l}},uploadType:{type:Number},uploadTempId:{type:String},uploadTemp:{type:String},height:{type:Number,default:800},fullpage:{type:Boolean,default:!0}},mounted:function(){},data:function(){var e=this;return{content:this.value,editorInit:{skin_url:"/static/tinymce/skins/ui/oxide",language_url:"/static/tinymce/langs/zh_CN.js",language:"zh_CN",height:this.height,branding:!1,menubar:!1,toolbar:this.toolbar,plugins:this.fullpage?[].concat(Object(i["a"])(this.plugins),["fullpage"]):this.plugins,images_upload_url:d["a"],image_uploadtab:!0,convert_fonts_to_spans:!1,valid_elements:"*[*]",extended_valid_elements:"html,head,meta[*],link[*],title,style[*],script[*],body,font[*],a[*]",valid_children:"+html[head|body],+head[title|style|script|meta|link],+body[style|script|font|a]",forced_root_block:"",custom_elements:"html,head,meta,link,title,style,script,body,font",content_css:!1,allow_html_in_named_anchor:!0,verify_html:!1,convert_urls:!1,entity_encoding:"raw",images_upload_handler:function(t,n,a){var r,i;r=new XMLHttpRequest,r.withCredentials=!1,r.open("POST",d["a"]),r.setRequestHeader("Access-Token",Object(m["c"])()),r.onload=function(){var e;200==r.status?(e=JSON.parse(r.responseText),e&&0==e.code?n(e.data.url):a("上传失败: "+r.responseText)):a("HTTP Error: "+r.status)},i=new FormData,i.append("uploadType","deliverTemplateImage"),i.append("uploadTemp",e.uploadTemp),i.append("uploadTempId",e.uploadTempId),i.append("file",t.blob(),t.filename()),r.send(i)},setup:function(t){t.ui.registry.addButton("uploadCssButton",{text:"上传CSS",icon:"new-document",onAction:function(){var n=document.createElement("input");n.type="file",n.accept=".css",n.onchange=function(n){var a=n.target.files[0];a&&e.uploadCss(a,t)},n.click()}})}}}},watch:{value:function(e){this.content=e},content:function(e){this.$emit("input",e)}},methods:{uploadCss:function(e,t){var n=this,a=new FormData;a.append("uploadType","deliverTemplateCss"),a.append("uploadTemp",this.uploadTemp),a.append("uploadTempId",this.uploadTempId),a.append("file",e),fetch(d["a"],{method:"POST",headers:{"Access-Token":Object(m["c"])()},body:a}).then((function(e){return e.json()})).then((function(e){if(0===e.code&&e.data.url){var a=e.data.url,r=t.getContent();r=r.replace("<head>",'<head><link rel="stylesheet" href="'.concat(a,'" />')),n.content=r,n.$xMsgSuccess("上传CSS成功")}})).catch((function(e){n.$xMsgError("CSS 上传失败："+e)}))}}},f=p,h=n("2877"),v=Object(h["a"])(f,a,r,!1,null,null,null);t["a"]=v.exports},"837b":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-alert",{attrs:{title:"说明",type:"warning",closable:!1}},[n("div",[e._v("\n      用于生成数据库表对应的实体类、后台CRUD代码\n    ")])]),e._v(" "),n("el-card",{staticClass:"mt-20"},[n("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},e._l(e.dbList,(function(e){return n("el-tab-pane",{key:e.value,attrs:{label:e.label}})})),1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{autoLoad:!1,data:e.tableData,"row-key":"id",loadData:e.getList}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"tableName",label:"表名"}}),e._v(" "),n("el-table-column",{attrs:{prop:"tableComment",label:"表描述"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(n){return e.handlePreview(t.row)}}},[e._v("预览")]),e._v(" "),n("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(n){return e.handleDownload(t.row)}}},[e._v("下载")])]}}])})],1)],1),e._v(" "),n("preview-dialog",{ref:"preview"})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("99b0"),o=n("365c"),l=(n("4624"),n("a481"),n("3b2b"),n("bc3a")),c=n.n(l),u=n("5f87"),d=n("5c96"),m={xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",zip:"application/zip"},p="/api";function f(e,t,n){var a,r=p+t;e&&(a=d["Loading"].service()),c()({method:"get",url:r,responseType:"blob",headers:{"Access-Token":Object(u["c"])()}}).then((function(t){e&&a.close(),h(t,m.zip)}))}function h(e,t){var n=document.createElement("a"),a=new Blob([e.data],{type:t}),r=new RegExp("filename=([^;]+\\.[^\\.;]+);*"),i=decodeURI(e.headers["content-disposition"]),s=r.exec(i),o=s[1];o=o.replace(/\"/g,""),n.href=URL.createObjectURL(a),n.setAttribute("download",o),document.body.appendChild(n),n.click(),document.body.appendChild(n)}var v={components:{PreviewDialog:s["default"]},mixins:[],data:function(){return{loading:!1,tableData:{},queryParams:{},tabName:"",dbList:[],canDownload:!0}},mounted:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.loadData();case 2:this.reloadByDbIndex(0);case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["genApi"].getTableList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),loadData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["y"].getDatabase();case 2:t=e.sent,this.dbList=t.data;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),reloadByDbIndex:function(e){this.queryParams.database=this.dbList[e].value,this.$refs.table.refresh(!0)},handleTabClick:function(e){this.reloadByDbIndex(e.index)},handlePreview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n={tableName:t.tableName,database:this.queryParams.database},this.$refs.preview.open(n);case 2:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDownload:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.canDownload){e.next=3;break}return this.$xMsgError("操作太快了，稍候再试~"),e.abrupt("return");case 3:({tableName:t.tableName,database:this.queryParams.database}),n="/system/gen/download?tableName=".concat(t.tableName,"&database=").concat(this.queryParams.database),f(!0,n),this.canDownload=!1,setTimeout((function(){a.canDownload=!0}),6e3);case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},g=v,b=n("2877"),w=Object(b["a"])(g,a,r,!1,null,null,null);t["default"]=w.exports},"83d6a":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{width:"1100px",title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"150px",size:"large"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"批量添加工单配置",prop:"counterOrderConfigString"}},[n("el-input",{staticStyle:{width:"900px"},attrs:{type:"textarea",autosize:{minRows:20},placeholder:"请输入工单单配置批量添加字符串，格式：URL@Pwd@Title换行URL2@Pwd2@Title"},model:{value:e.form.counterOrderConfigString,callback:function(t){e.$set(e.form,"counterOrderConfigString",t)},expression:"form.counterOrderConfigString"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="批量新增",this.visible=!0},close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=8;break}return e.$xloading.show(),a=Object.assign({},e.form),t.next=5,o["c"].batchCounterOrderConfig(a);case 5:r=t.sent,e.$xloading.hide(),0==r.code?(e.$xMsgSuccess("批量添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+r.msg);case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},8490:function(e,t,n){"use strict";n("1ccc")},8895:function(e,t,n){},"89ee":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"上级菜单",prop:"parentId"}},[n("treeselect",{attrs:{options:e.parentMenus,placeholder:"选择上级菜单"},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"菜单类型",prop:"menuType"}},[n("el-radio-group",{model:{value:e.form.menuType,callback:function(t){e.$set(e.form,"menuType",t)},expression:"form.menuType"}},[n("el-radio",{attrs:{label:0}},[e._v("目录")]),e._v(" "),n("el-radio",{attrs:{label:1}},[e._v("菜单")]),e._v(" "),n("el-radio",{attrs:{label:2}},[e._v("按钮")])],1)],1)],1),e._v(" "),n("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[n("el-form-item",{attrs:{label:"菜单名称",prop:"menuName"}},[n("el-input",{attrs:{placeholder:"请输入菜单名称"},model:{value:e.form.menuName,callback:function(t){e.$set(e.form,"menuName",t)},expression:"form.menuName"}})],1)],1),e._v(" "),n("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[n("el-form-item",{attrs:{label:"显示排序",prop:"sort"}},[n("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),"2"!=e.form.menuType?n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"菜单图标",prop:"icon"}},[n("el-input",{attrs:{placeholder:"请输入图标类名"},model:{value:e.form.icon,callback:function(t){e.$set(e.form,"icon",t)},expression:"form.icon"}},[n("template",{slot:"prepend"},[n("x-icon",{attrs:{icon:e.form.icon}})],1),e._v(" "),n("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:e.openIconsDialog},slot:"append"})],2)],1)],1):e._e(),e._v(" "),"2"!=e.form.menuType?n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"路由地址",prop:"path"}},[n("el-input",{attrs:{placeholder:"请输入路由地址"},model:{value:e.form.path,callback:function(t){e.$set(e.form,"path",t)},expression:"form.path"}})],1)],1):e._e(),e._v(" "),"1"==e.form.menuType?n("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[n("el-form-item",{attrs:{label:"组件路径",prop:"component"}},[n("el-input",{attrs:{placeholder:"请输入组件路径"},model:{value:e.form.component,callback:function(t){e.$set(e.form,"component",t)},expression:"form.component"}})],1)],1):e._e(),e._v(" "),n("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},["0"!=e.form.menuType?n("el-form-item",{attrs:{label:"权限标识",prop:"permission"}},[n("el-input",{attrs:{placeholder:"请权限标识",maxlength:"50"},model:{value:e.form.permission,callback:function(t){e.$set(e.form,"permission",t)},expression:"form.permission"}})],1):e._e()],1),e._v(" "),"2"!=e.form.menuType?n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"菜单状态",prop:"isShow"}},[n("el-radio-group",{model:{value:e.form.isShow,callback:function(t){e.$set(e.form,"isShow",t)},expression:"form.isShow"}},[n("el-radio",{attrs:{label:!0}},[e._v("显示")]),e._v(" "),n("el-radio",{attrs:{label:!1}},[e._v("隐藏")])],1)],1)],1):e._e()],1)],1),e._v(" "),n("icons-dialog",{attrs:{visible:e.iconsDialogVisible,current:e.form.icon},on:{"update:visible":function(t){e.iconsDialogVisible=t},select:e.onIconSelect}})],1)},r=[],i=(n("96cf"),n("1da1")),s=(n("8e6e"),n("ac6a"),n("456d"),n("ade3")),o=n("2f62");n("d0a2"),n("de11");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){Object(s["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={computed:c({},Object(o["d"])({device:function(e){return e.app.device}})),mounted:function(){this.noFitEditDialogWidth||(this.isMobile()?this.dialogWidth="100%":this.dialogWidth="800px")},methods:{isMobile:function(){return"mobile"==this.device},isDesktop:function(){return"desktop"==this.device}}},d=n("a6dc"),m=n("ca17"),p=n.n(m),f=(n("542c"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",e._g(e._b({attrs:{"append-to-body":"",width:e.dialogWidth,"custom-class":"icon-dialog"},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[n("div",{attrs:{slot:"title"},slot:"title"},[e._v("\n      选择图标\n      "),n("el-input",{style:{width:"260px"},attrs:{size:"mini",placeholder:"请输入图标名称","prefix-icon":"el-icon-search",clearable:""},model:{value:e.key,callback:function(t){e.key=t},expression:"key"}})],1),e._v(" "),n("el-tabs",[n("el-tab-pane",{attrs:{label:"element-ui"}},[n("ul",{staticClass:"icon-ul"},e._l(e.iconList,(function(t){return n("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(n){return e.onSelect(t)}}},[n("i",{class:t}),e._v(" "),n("div",[e._v(e._s(t))])])})),0)]),e._v(" "),n("el-tab-pane",{attrs:{label:"自定义"}},[n("ul",{staticClass:"icon-ul"},e._l(e.svgIconList,(function(t){return n("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(n){return e.onSelect(t)}}},[n("svg-icon",{attrs:{"icon-class":t}}),e._v(" "),n("div",[e._v(e._s(t))])],1)})),0)])],1)],1)],1)}),h=[],v=n("8206"),g=v.map((function(e){return"el-icon-".concat(e)})),b=[],w=n("51ff");w.keys().forEach((function(e){var t=e.substring(2,e.length-4);b.push(t)}));var _={inheritAttrs:!1,props:["current"],data:function(){return{dialogWidth:"980px",iconList:g,svgIconList:b,active:null,key:""}},watch:{key:function(e){this.iconList=e?g.filter((function(t){return t.indexOf(e)>-1})):g}},methods:{onOpen:function(){this.active=this.current,this.key=""},onClose:function(){},onSelect:function(e){this.active=e,this.$emit("select",e),this.$emit("update:visible",!1)}}},y=_,x=(n("db6a"),n("1cfa"),n("2877")),k=Object(x["a"])(y,f,h,!1,null,"7abf13ca",null),$=k.exports,O={components:{Treeselect:p.a,IconsDialog:$},mixins:[u],data:function(){return{formItemCol:{xs:24,md:12},title:"",visible:!1,loading:!1,form:{},rules:{menuType:[{required:!0,message:"不能为空",trigger:"blur"}],menuName:[{required:!0,message:"不能为空",trigger:"blur"}],sort:[{required:!0,message:"不能为空",trigger:"blur"}],path:[{required:!0,message:"不能为空",trigger:"blur"}],isShow:[{required:!0,message:"不能为空",trigger:"blur"}],systemCode:[{required:!0,message:"不能为空",trigger:"blur"}]},parentMenus:[],iconsDialogVisible:!1}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),mounted:function(){},methods:{add:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="新增",this.visible=!0,e.next=5,this.getTreeData();case 5:t&&this.$set(this.form,"parentId",t.id);case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,this.getTreeData();case 6:return e.next=8,d["getMenu"](t.id);case 8:n=e.sent,0==n.code&&(this.form=n.data,this.$xloading.hide());case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={isShow:!0},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=15;break}if(e.$xloading.show(),void 0==e.form.id){t.next=10;break}return t.next=5,d["editMenu"](e.form);case 5:a=t.sent,e.$xloading.hide(),0==a.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+a.msg),t.next=15;break;case 10:return t.next=12,d["addMenu"](e.form);case 12:r=t.sent,e.$xloading.hide(),0==r.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+r.msg);case 15:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getTreeData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d["getTreeData"](!0);case 2:t=e.sent,this.parentMenus=t.data;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),openIconsDialog:function(){this.iconsDialogVisible=!0},onIconSelect:function(e){this.form.icon=e}}},C=O,R=Object(x["a"])(C,a,r,!1,null,null,null);t["default"]=R.exports},"8cdb":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"wscn-http404-container"},[n("div",{staticClass:"wscn-http404"},[e._m(0),e._v(" "),n("div",{staticClass:"bullshit"},[n("div",{staticClass:"bullshit__oops"},[e._v("呀!!")]),e._v(" "),n("div",{staticClass:"bullshit__info"}),e._v(" "),n("div",{staticClass:"bullshit__headline"},[e._v(e._s(e.message))]),e._v(" "),n("div",{staticClass:"bullshit__info"},[e._v("\n        页面不存在，请检查链接是否正确，或者点击下面的按钮返回首页\n      ")]),e._v(" "),n("a",{staticClass:"bullshit__return-home",attrs:{href:"/"}},[e._v("返回首页")])])])])},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pic-404"},[a("img",{staticClass:"pic-404__parent",attrs:{src:n("a36b"),alt:"404"}}),e._v(" "),a("img",{staticClass:"pic-404__child left",attrs:{src:n("26fc"),alt:"404"}}),e._v(" "),a("img",{staticClass:"pic-404__child mid",attrs:{src:n("26fc"),alt:"404"}}),e._v(" "),a("img",{staticClass:"pic-404__child right",attrs:{src:n("26fc"),alt:"404"}})])}],i={name:"Page404",computed:{message:function(){return"这里什么都没有..."}}},s=i,o=(n("c89d"),n("2877")),l=Object(o["a"])(s,a,r,!1,null,"e6261d8c",null);t["default"]=l.exports},"8f91":function(e,t,n){"use strict";n("e702")},"952f":function(e,t,n){},"96ca":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"账号名称"}},[n("el-input",{model:{value:e.queryParams.account_name,callback:function(t){e.$set(e.queryParams,"account_name",t)},expression:"queryParams.account_name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"广告名称"}},[n("el-input",{model:{value:e.queryParams.ad_name,callback:function(t){e.$set(e.queryParams,"ad_name",t)},expression:"queryParams.ad_name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"账号",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.account_name))]),e._v(" "),n("br"),e._v(" "),n("el-tag",{attrs:{type:"default",size:"medium"}},[e._v(e._s(a.account_id))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"用户",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.fbUserName))]),e._v(" "),n("br"),e._v(" "),n("el-tag",{attrs:{type:"default",size:"medium"}},[e._v(e._s(a.fbUserId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.ad_name))]),e._v(" "),n("br"),e._v(" "),n("el-tag",{attrs:{type:"default",size:"medium"}},[e._v(e._s(a.ad_id))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"花费",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"coupon-price"},[e._v(e._s(a.spend.toFixed(2))+" "+e._s(a.account_currency))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"数据",align:"left","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",{staticClass:"price"},[e._v("CPM: "+e._s(a.cpm.toFixed(2)))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("CPC: "+e._s(a.cpc.toFixed(2)))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("CTR: "+e._s(a.ctr.toFixed(2)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"展示数据",align:"left","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",{staticClass:"price"},[e._v("展示次数: "+e._s(a.impressions))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("覆盖人数: "+e._s(a.reach))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("频次: "+e._s(a.frequency.toFixed(2)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"点击数据",align:"left","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",{staticClass:"price"},[e._v("总点击: "+e._s(a.clicks))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("链接点击: "+e._s(a.inline_link_clicks))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"播放率",align:"left","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",{staticClass:"price"},[e._v("播放25%: "+e._s(null==a.video_p25_watched_actions_value?0:a.video_p25_watched_actions_value))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("播放50%: "+e._s(null==a.video_p50_watched_actions_value?0:a.video_p50_watched_actions_value))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("播放75%: "+e._s(null==a.video_p75_watched_actions_value?0:a.video_p75_watched_actions_value))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("播放95%: "+e._s(null==a.video_p95_watched_actions_value?0:a.video_p95_watched_actions_value))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"price"},[e._v("播放100%: "+e._s(null==a.video_p100_watched_actions_value?0:a.video_p100_watched_actions_value))])]}}])})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("5a0c"),o=n.n(s),l=n("4624"),c=n("365c"),u={mixins:[l["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},channelOptions:[]}},created:function(){this.reset();var e=this.$router.history.current.query;e.account_id&&(this.queryParams.account_id=e.account_id)},methods:{queryReset:function(){this.queryParams={},this.reset(),this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["v"].getList(t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){var e=o()().format("YYYY-MM-DD"),t=o()().format("YYYY-MM-DD");this.queryParams.date=[e,t]}}},d=u,m=(n("cc1b"),n("2877")),p=Object(m["a"])(d,a,r,!1,null,"********",null);t["default"]=p.exports},"99b0":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"1200px"},on:{"update:visible":function(t){e.visible=t},cancel:function(t){e.visible=!1}}},[n("el-tabs",[n("el-tab-pane",{attrs:{label:"Entity.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.entity,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.entity)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"Table.md"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.tableMd,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.tableMd)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"ResponseVO.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.responseVO,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.responseVO)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"RequestParams.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.requestParams,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.requestParams)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"IRepository.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.iRepository,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.iRepository)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"Repository.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.repository,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.repository)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"IService.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.iService,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.iService)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"Service.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.service,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.service)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"Controller.cs"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.controller,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.controller)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"api.js"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.api,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.api)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"index.vue"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.index,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.index)+" ")])],1),e._v(" "),n("el-tab-pane",{attrs:{label:"Edit.vue"}},[n("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.edit,t)}}},[e._v("复制代码")]),e._v(" "),n("pre",[e._v(" "+e._s(e.data.edit)+" ")])],1)],1)],1)},r=[],i=(n("a481"),n("3b2b"),n("96cf"),n("1da1")),s=n("2b0e"),o=n("b311"),l=n.n(o);function c(){s["default"].prototype.$message({message:"复制成功",type:"success",duration:1500})}function u(){s["default"].prototype.$message({message:"复制失败",type:"error"})}function d(e,t){var n=new l.a(t.target,{text:function(){return e}});n.on("success",(function(){c(),n.destroy()})),n.on("error",(function(){u(),n.destroy()})),n.onClick(t)}var m=n("365c"),p={data:function(){return{title:"预览",visible:!1,loading:!1,data:{}}},methods:{open:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.data={},this.visible=!0,this.$xloading.show(),e.next=5,m["genApi"].getCodePreview(t);case 5:n=e.sent,this.$xloading.hide(),this.data=n.data;case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),replaceEnter:function(e){var t=new RegExp("\r\n","g");return e.replace(t,"<br/>")},handleCopy:function(e,t){d(e,t)}}},f=p,h=(n("23c5"),n("2877")),v=Object(h["a"])(f,a,r,!1,null,"4dc2954c",null);t["default"]=v.exports},"9abd":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"table-cell-content"},[n("span",{staticClass:"sc-id"},[e._v(e._s(e.cId))]),e._v(" "),n("br"),e._v(" "),n("span",{staticClass:"sc-name"},[e._v(e._s(e.cName))])])},r=[],i={data:function(){return{}},props:["cId","cName"],methods:{}},s=i,o=(n("c846"),n("2877")),l=Object(o["a"])(s,a,r,!1,null,"29a727d4",null);t["a"]=l.exports},"9ed6":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"login"},[n("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[n("h3",{staticClass:"title"},[e._v(e._s(this.title)+"登录")]),e._v(" "),n("el-form-item",{attrs:{prop:"username"}},[n("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[n("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),e._v(" "),n("el-form-item",{attrs:{prop:"password"}},[n("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[n("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),e._v(" "),n("el-checkbox",{staticStyle:{margin:"0px 0px 0px 0px"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住我")]),e._v(" "),n("el-form-item",{staticClass:"login-btn",staticStyle:{width:"100%","margin-bottom":"10px"}},[n("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,size:"medium",type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?n("span",[e._v("登 录 中...")]):n("span",[e._v("登 录")])])],1),e._v(" "),e.isAdminUser?n("el-form-item",[n("el-button",{staticStyle:{width:"100%"},attrs:{size:"medium"},nativeOn:{click:function(t){return t.preventDefault(),e.handleOAuth(t)}}},[n("span",[e._v("用户中心登录")])])],1):e._e()],1),e._v(" "),e._m(0)],1)},r=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"el-login-footer"},[n("span")])}],i=(n("96cf"),n("1da1")),s=(n("aef6"),n("83d6")),o=n.n(s),l=(n("a78e"),n("7d92")),c=n("5f87"),u=n("365c"),d={name:"Login",data:function(){var e=function(e,t,n){0==t.length?n(new Error("用户名不能为空")):n()},t=function(e,t,n){0==t.length?n(new Error("密码不能为空")):n()};return{title:o.a.title,loginForm:{username:"",password:""},loginRules:{username:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:t}]},loading:!1,passwordType:"password",redirect:void 0,isAdminUser:!1}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.checkIframe(),this.getUserLoginParams(),window.location.hostname.endsWith("dodourl.com")&&(this.isAdminUser=!0)},methods:{checkIframe:function(){if(window.self!==window.top){var e={url:window.location.origin};window.parent.postMessage({type:"OpenTab",data:e},"*")}},getUserLoginParams:function(){var e=Object(c["d"])(),t=Object(c["a"])(),n=Object(c["b"])();this.loginForm={username:null===e?this.loginForm.username:e,password:null===t?this.loginForm.password:Object(l["a"])(t),rememberMe:null!==n&&Boolean(n)}},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.password.focus()}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){if(!t)return!1;e.$xloading.show();var n=Object.assign({},e.loginForm);e.loginForm.rememberMe?(Object(c["k"])(e.loginForm.username),Object(c["h"])(Object(l["b"])(e.loginForm.password)),Object(c["i"])(e.loginForm.rememberMe)):Object(c["g"])(),n.password=Object(l["c"])(n.password),e.$store.dispatch("Login",n).then((function(){e.$router.push({path:e.redirect||"/"}),e.$xloading.hide(),window.location.reload()})).catch((function(t){e.$xloading.hide(),e.$xMsgError(t)}))}))},handleOAuth:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$loading(),e.next=3,u["a"].getYYAuthUrl();case 3:n=e.sent,t.close(),0==n.code?(a=n.data,window.location.href=a):this.$xMsgError(n.msg);case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},m=d,p=(n("b4b1"),n("2877")),f=Object(p["a"])(m,a,r,!1,null,null,null);t["default"]=f.exports},"9fad":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"广告ID"}},[n("el-input",{model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"广告账户ID"}},[n("el-input",{model:{value:e.queryParams.account_id,callback:function(t){e.$set(e.queryParams,"account_id",t)},expression:"queryParams.account_id"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"primary"},on:{click:e.handleActiveAd}},[e._v("开启广告")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"warning"},on:{click:e.handlePausedAd}},[e._v("暂停广告")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":{fontSize:"13px"}},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{label:"广告名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.name,secondText:t.id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告组名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.adset_name,secondText:t.adset_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"广告系列名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.campaign_name,secondText:t.campaign_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账户名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.account_name,secondText:t.account_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"bid_amount_cal",label:"出价金额",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"effective_status",label:"生产状态",align:"center","min-width":"130"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["ACTIVE"==a.status?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):"PAUSED"==a.status?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("暂停")]):"DELETED"==a.status?n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("删除")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("归档")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"创建时间/更新时间",align:"center","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.created_time))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.updated_time))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"lastUpdateTime",label:"最后抓取时间",align:"center","min-width":"160"}})],1)],1)],1)},r=[],i=(n("ac6a"),n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("7c49"),c={components:{TwoRowColumn:l["a"]},mixins:[s["a"]],data:function(){return{selections:[],queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["q"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["q"].delFBAd(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.selections=e.map((function(e){return{id:e.id,account_id:e.account_id}}))},handleActiveAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认开启广告？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"ACTIVE"},t.next=4,o["q"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")},handlePausedAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认暂停广告？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"PAUSED"},t.next=4,o["q"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},a1db:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"广告系列ID"}},[n("el-input",{model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"广告账户ID"}},[n("el-input",{model:{value:e.queryParams.account_id,callback:function(t){e.$set(e.queryParams,"account_id",t)},expression:"queryParams.account_id"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"primary"},on:{click:e.handleActiveAd}},[e._v("开启广告系列")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBAd:changestatus"],expression:"['fBAd:changestatus']"}],attrs:{size:"mini",type:"warning"},on:{click:e.handlePausedAd}},[e._v("暂停广告系列")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":{fontSize:"13px"}},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{label:"广告系列名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.name,secondText:t.id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账户名称/ID",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("two-row-column",{attrs:{firstText:t.account_name,secondText:t.account_id}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"每日预算/剩余预算/支出上限",align:"center","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.daily_budget))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.budget_remaining))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.spend_cap))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"预算",align:"center","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.is_budget_schedule_enabled?n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v("使用广告系列预算")]):n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v("使用广告组预算")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"超出预算",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.can_use_spend_cap?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("会")]):n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"effective_status",label:"生产状态",align:"center","min-width":"130"}}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["ACTIVE"==a.status?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):"PAUSED"==a.status?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("暂停")]):"DELETED"==a.status?n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("删除")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("归档")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"开始时间/结束时间",align:"center","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.start_time))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.stop_time))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"创建时间/更新时间",align:"center","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.created_time))]),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.updated_time))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"lastUpdateTime",label:"最后抓取时间",align:"center","min-width":"130"}})],1)],1)],1)},r=[],i=(n("ac6a"),n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("7c49"),c={components:{TwoRowColumn:l["a"]},mixins:[s["a"]],data:function(){return{selections:[],queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["r"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["r"].delFBAdCampaign(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.selections=e.map((function(e){return{id:e.id,account_id:e.account_id}}))},handleActiveAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认开启广告系列？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"ACTIVE"},t.next=4,o["r"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")},handlePausedAd:function(){var e=this;this.selections.length>0?this.$confirm("是否确认暂停广告系列？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.selections.forEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$xloading.show(),a={id:n.id,account_id:n.account_id,status:"PAUSED"},t.next=4,o["r"].changeStatus(a);case 4:r=t.sent,e.$xloading.hide(),0==r.code?e.$xMsgSuccess("操作成功"):e.$xMsgError("操作失败！"+r.msg);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e.$refs.table.refresh();case 2:case"end":return t.stop()}}),t)})))).catch((function(){e.$xloading.hide()})):this.$xMsgWarning("请选择要操作的广告！")}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},a36b:function(e,t,n){e.exports=n.p+"static/img/404.a57b6f31.png"},a457:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var a=n("b775");function r(e){return Object(a["a"])({url:"/Log/LoginLog",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/Log/OperLog",method:"get",params:e})}},a7c9:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-refresh"},on:{click:e.handleStats}},[e._v("重新统计")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-class-name":e.highlightChildren},on:{"expand-change":e.handleExpandChange}},[n("el-table-column",{attrs:{label:"日期",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"areaName",label:"地区",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.cId,cName:t.cName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"totalTarget",label:"目标",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueNewFans",label:"进线",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"changeNewFans",label:"调粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"bannedNewFans",label:"封号",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"residue",label:"剩余(容量)",sortable:"",align:"center","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"totalFans",label:"总粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueRepeatFans",label:"重复",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"重复率",align:"center",sortable:"","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.repeatFansPro)+"%")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"单价",align:"center","sortablemin-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.price)+" 元")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"taskCount",label:"任务数",sortable:"",align:"center","min-width":"100"}})],1)],1)],1)},r=[],i=(n("c5f6"),n("96cf"),n("1da1")),s=(n("ac6a"),n("5df3"),n("4f7f"),n("9abd")),o=n("4624"),l=n("365c"),c={components:{ChannelInfo:s["a"]},mixins:[o["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{},expandedChildren:new Set}},created:function(){},methods:{queryReset:function(){this.queryParams={date:(new Date).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["i"].getChannelStatsList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getSummaries:function(e){var t=e.columns,n=e.data,a=[];return t.forEach((function(e,t){if(0!==t){var r=n.map((function(t){return Number(t[e.property])}));r.every((function(e){return isNaN(e)}))?a[t]="--":a[t]=r.reduce((function(e,t){return e+t}),0)}else a[t]="合计"})),a},handleExpandChange:function(e,t){var n=this;t?e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.add(e.id)})):e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.delete(e.id)}))},highlightChildren:function(e){var t=e.row;return this.expandedChildren.has(t.id)?"highlight-row":""},handleStats:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.queryParams.date,e.next=3,l["j"].stats(t);case 3:n=e.sent,0===n.code?this.$xMsgSuccess("重新统计成功"):this.$xMsgError(n.msg);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},u=c,d=(n("2e5c"),n("2877")),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},aa34:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"Type"}},[n("el-input",{attrs:{value:"2"},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{options:e.areaOptions},on:{change:e.fetchTaskConfigOption},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"任务"}},[n("x-select",{attrs:{options:e.taskConfigOptions},model:{value:e.queryParams.taskConfigId,callback:function(t){e.$set(e.queryParams,"taskConfigId",t)},expression:"queryParams.taskConfigId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"渠道标识"}},[n("el-input",{model:{value:e.queryParams.cid,callback:function(t){e.$set(e.queryParams,"cid",t)},expression:"queryParams.cid"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"主号"}},[n("el-input",{model:{value:e.queryParams.account,callback:function(t){e.$set(e.queryParams,"account",t)},expression:"queryParams.account"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"IP"}},[n("el-input",{model:{value:e.queryParams.ip,callback:function(t){e.$set(e.queryParams,"ip",t)},expression:"queryParams.ip"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"taskName",label:"任务名称/地区",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("task-info",{attrs:{taskId:t.taskId,taskName:t.taskName,areaName:t.areaName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"渠道",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("channel-info",{attrs:{cId:t.cId,cName:t.cName}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"ip",label:"IP/国家/城市",align:"center","min-width":"280"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"ip-location-info"},[n("div",{staticClass:"ip-row"},[n("i",{staticClass:"el-icon-connection",staticStyle:{color:"#409eff","margin-right":"5px"}}),e._v(" "),n("el-tag",{attrs:{type:"primary",size:"small",effect:"light"}},[e._v(e._s(a.ip))])],1),e._v(" "),a.country?n("div",{staticClass:"location-row"},[n("i",{staticClass:"el-icon-location",staticStyle:{color:"#67c23a","margin-right":"5px"}}),e._v(" "),n("el-tag",{attrs:{type:"success",size:"mini",effect:"plain"}},[e._v(e._s(a.country))])],1):e._e(),e._v(" "),a.city?n("div",{staticClass:"location-row"},[n("i",{staticClass:"el-icon-map-location",staticStyle:{color:"#e6a23c","margin-right":"5px"}}),e._v(" "),n("el-tag",{attrs:{type:"warning",size:"mini",effect:"plain"}},[e._v(e._s(a.city))])],1):e._e()])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"status-column"},[n("div",{staticClass:"status-item"},[n("i",{staticClass:"el-icon-cpu",staticStyle:{color:"#909399","margin-right":"3px","font-size":"12px"}}),e._v(" "),n("el-tag",{staticClass:"status-tag",attrs:{type:a.isIP?"success":"info",size:"mini",effect:"dark"}},[e._v("\n                IP"+e._s(a.isIP?"✓":"✗")+"\n              ")])],1),e._v(" "),n("div",{staticClass:"status-item"},[n("i",{staticClass:"el-icon-user",staticStyle:{color:"#909399","margin-right":"3px","font-size":"12px"}}),e._v(" "),n("el-tag",{staticClass:"status-tag",attrs:{type:a.isUV?"success":"info",size:"mini",effect:"dark"}},[e._v("\n                UV"+e._s(a.isUV?"✓":"✗")+"\n              ")])],1)])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"主号/像素",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"account-pixel-info"},[n("div",{staticClass:"account-row"},[n("i",{staticClass:"el-icon-user",staticStyle:{color:"#409eff","margin-right":"5px"}}),e._v(" "),n("el-tag",{attrs:{type:"primary",size:"small",effect:"light"}},[e._v(e._s(a.account||"无主号"))])],1),e._v(" "),n("div",{staticClass:"pixel-row"},[n("i",{staticClass:"el-icon-view",staticStyle:{color:"#67c23a","margin-right":"5px"}}),e._v(" "),n("el-tag",{attrs:{type:"success",size:"mini",effect:"plain"}},[e._v(e._s(a.pixelId||"无像素"))])],1)])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"utmSource",label:"来源",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"入库时间",align:"center","min-width":"180"}}),e._v(" "),n("el-table-column",{attrs:{label:"来源URL",align:"center","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"referrer-column"},[t.row.referrer?n("div",{staticClass:"referrer-content"},[n("div",{staticClass:"referrer-url",attrs:{title:t.row.referrer}},[n("a",{attrs:{href:t.row.referrer,target:"_blank"}},[e._v(e._s(t.row.referrer))])])]):n("div",{staticClass:"no-referrer-content"},[n("span",{staticClass:"no-referrer-text"},[e._v(" 无来源URL ")])])])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"operation-buttons"},[n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleOpenWeb(t.row,"ipapi")}}},[e._v("ipapi")]),e._v(" "),n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleOpenWeb(t.row,"ipinfo")}}},[e._v("ipinfo")])],1)]}}])})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("809c"),c=n("9abd"),u={components:{TaskInfo:l["a"],ChannelInfo:c["a"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},areaOptions:[],taskConfigOptions:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$router.history.current.query,t.date&&(this.queryParams.date=t.date),t.type&&(this.queryParams.type=t.type),t.areaId&&(this.queryParams.areaId=parseInt(t.areaId)),t.taskConfigId&&(this.queryParams.taskConfigId=parseInt(t.taskConfigId)),t.cid&&(this.queryParams.cid=t.cid),t.account&&(this.queryParams.account=t.account),t.ip&&(this.queryParams.ip=t.ip),e.next=10,this.fetchAreaOption();case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),watch:{"queryParams.areaId":{handler:"fetchTaskConfigOption",immediate:!0}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["A"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),fetchAreaOption:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["e"].getAreaOptions();case 2:t=e.sent,t&&0==t.code?this.areaOptions=t.data:this.$xMsgError(t.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),fetchTaskConfigOption:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["y"].getTaskConfigOptions({areaId:this.queryParams.areaId});case 2:t=e.sent,t&&0==t.code?this.taskConfigOptions=t.data:this.$xMsgError(t.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleOpenWeb:function(e,t){var n=e.ip;"ipapi"==t?window.open("https://ipapi.co/".concat(n,"/json/")):"ipinfo"==t&&window.open("https://ipinfo.io/".concat(n,"/json"))},isValidUrl:function(e){try{return new URL(e),!0}catch(t){return!1}}}},d=u,m=(n("c729"),n("2877")),p=Object(m["a"])(d,a,r,!1,null,"aa48663c",null);t["default"]=p.exports},b4b1:function(e,t,n){"use strict";n("691a")},b6c6:function(e,t,n){},b803:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["category:add"],expression:"['category:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{prop:"id",label:"id",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"名称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"sort",label:"排序",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["category:edit"],expression:"['category:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["category:delete"],expression:"['category:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("74f3"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["b"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["b"].delCategory(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},b943:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:16}},[n("el-form-item",{attrs:{label:"调粉渠道标识",prop:"cid"}},[n("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"fetch-suggestions":e.querySearch,placeholder:"请输入调粉的目标渠道标识","trigger-on-focus":!1,debounce:50,"prefix-icon":"el-icon-search"},on:{select:e.handleSelect},model:{value:e.form.cid,callback:function(t){e.$set(e.form,"cid",t)},expression:"form.cid"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"进行调粉的主号",prop:"accounts"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:8},placeholder:"请输入进行调粉的主号"},model:{value:e.formattedAccounts,callback:function(t){e.formattedAccounts=t},expression:"formattedAccounts"}})],1)],1)],1)],1)],1)},r=[],i=(n("7f7f"),n("28a5"),n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l={components:{},data:function(){return{ids:[],title:"",visible:!1,loading:!1,accounts:[],form:{accounts:[]},channelOptions:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["c"])();case 2:this.channelOptions=e.sent;case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),computed:{formattedAccounts:{get:function(){return this.accounts.join("\n")},set:function(e){this.accounts=e.split("\n")}}},methods:{edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,n){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.reset(),this.title="调粉操作",this.visible=!0,this.form.id=n,this.accounts=t;case 5:case"end":return e.stop()}}),e,this)})));function t(t,n){return e.apply(this,arguments)}return t}(),reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=4;break}return a=Object.assign({},e.form,{accounts:e.accounts}),t.next=4,e.handleChangeFans(a);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleChangeFans:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n="是否确认进行调粉操作，把主号【".concat(this.accounts.join(","),"】调到渠道【").concat(this.form.cid,"】？"),this.$confirm(n,"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,o["j"].changeFans(t);case 3:n=e.sent,a.$xloading.hide(),0==n.code?(a.$xMsgSuccess("操作成功"),a.visible=!1,a.$emit("ok")):a.$xMsgError("操作失败！"+n.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 2:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),querySearch:function(e,t){var n=this.channelOptions,a=e?n.filter((function(t){return t.value.toLowerCase().indexOf(e.toLowerCase())>-1||t.label.toLowerCase().indexOf(e.toLowerCase())>-1})):n;a=a.map((function(e){return{value:e.label+" 【"+e.value+"】",name:e.value}})),t(a)},handleSelect:function(e){this.form.cid=e.name}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},b97c:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{page:!1,data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-class-name":e.highlightChildren},on:{"expand-change":e.handleExpandChange}},[e._v("\n      >\n      "),n("el-table-column",{attrs:{label:"日期",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"地区",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.areaName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"taskConfigName",label:"任务",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{label:"进线"}},[n("el-table-column",{attrs:{label:"IP/UV/PV",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handlePageCollectRecord(a)}}},[n("span",[e._v(e._s(a.ip)+" / "+e._s(a.uv)+" / "+e._s(a.pv))])])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"未知"}},[n("el-table-column",{attrs:{prop:"none",label:"未知",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.nonePer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"FB"}},[n("el-table-column",{attrs:{prop:"fb",label:"FB",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.fbPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"INS"}},[n("el-table-column",{attrs:{prop:"ins",label:"INS",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.insPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"Snap"}},[n("el-table-column",{attrs:{prop:"snap",label:"Snap",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.snapPer)+"%")])]}}])})],1),e._v(" "),n("el-table-column",{attrs:{label:"KW"}},[n("el-table-column",{attrs:{prop:"kw",label:"KW",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"比例",sortable:"",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.kwPer)+"%")])]}}])})],1)],1)],1)],1)},r=[],i=(n("c5f6"),n("96cf"),n("1da1")),s=(n("ac6a"),n("5df3"),n("4f7f"),n("4624")),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{},expandedChildren:new Set}},created:function(){},methods:{queryReset:function(){this.queryParams={date:(new Date).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["i"].getDeliverPageTaskDateSummary(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getSummaries:function(e){var t=e.columns,n=e.data,a=[];return t.forEach((function(e,t){if(0!==t){var r=n.map((function(t){return Number(t[e.property])}));r.every((function(e){return isNaN(e)}))?a[t]="--":a[t]=r.reduce((function(e,t){return e+t}),0)}else a[t]="合计"})),a},handleExpandChange:function(e,t){var n=this;t?e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.add(e.id)})):e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.delete(e.id)}))},highlightChildren:function(e){var t=e.row;return this.expandedChildren.has(t.id)?"highlight-row":""},handlePageCollectRecord:function(e){this.$router.push({path:"/page/pagecollectrecord",query:{date:e.date,type:2,areaId:e.areaId,taskConfigId:e.taskConfigId,cid:e.cId}})}}},c=l,u=(n("499e"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},bb53:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"登录账号",prop:"userName"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1)],1),e._v(" "),e.isEdit?e._e():n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"密码",prop:"password"}},[n("el-input",{attrs:{placeholder:"","show-password":"",autocomplete:"new-password"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1)],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名",prop:"nickName"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"用户中心ID",prop:"authUId"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.form.authUId,callback:function(t){e.$set(e.form,"authUId",t)},expression:"form.authUId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"角色",prop:"roleIds"}},[n("x-checkbox",{attrs:{url:"/system/user/roleList",map:e.roleOptionsMap},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[n("el-radio",{attrs:{label:!0}},[e._v("启用")]),e._v(" "),n("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{type:"textarea",placeholder:""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{isEdit:!1,title:"",visible:!1,loading:!1,form:{},rules:{userName:[{required:!0,message:"登录账号不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符",trigger:"blur"}],nickName:[{required:!0,message:"姓名不能为空",trigger:"blur"}],roleIds:[{required:!0,message:"角色不能为空",trigger:"blur"}],systemCodes:[{required:!0,message:"系统权限不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]},roleOptions:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),methods:{add:function(){this.isEdit=!1,this.reset(),this.title="新增",this.visible=!0,this.getRoleOptions()},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.isEdit=!0,this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=7,this.getRoleOptions();case 7:return e.next=9,s["E"].getDetail(t.id);case 9:n=e.sent,0==n.code&&(this.$xloading.hide(),this.form=n.data,a=n.data.authUId,0==a&&(this.form.authUId=void 0));case 11:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={id:void 0,title:void 0,roleIds:[],systemCodes:[]},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,s["E"].editUser(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["E"].addUser(a);case 14:o=t.sent,e.$xloading.hide(),0==o.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+o.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},roleOptionsMap:function(e){return e.map((function(e){return{label:e.roleName,value:e.id}}))},getRoleOptions:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s["E"].getRoleList();case 2:t=e.sent,0==t.code&&(this.roleOptions=t.data.map((function(e){return{label:e.roleName,value:e.id}})));case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},l=o,c=n("2877"),u=Object(c["a"])(l,a,r,!1,null,null,null);t["default"]=u.exports},bbf2:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"域名"}},[n("el-input",{model:{value:e.queryParams.domain,callback:function(t){e.$set(e.queryParams,"domain",t)},expression:"queryParams.domain"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domainblacklist:add"],expression:"['domainblacklist:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"domain",label:"域名",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"editTime",label:"修改时间",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domainblacklist:edit"],expression:"['domainblacklist:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["domainblacklist:delete"],expression:"['domainblacklist:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("62ec"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["o"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["o"].delDomainBlackList(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},bf9e:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:16}},[n("el-form-item",{attrs:{label:"渠道标识",prop:"id"}},[n("el-input",{attrs:{disabled:e.isDisabled,placeholder:"请输入渠道标识"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"渠道名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入渠道名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:6},placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l={data:function(){return{title:"",isDisabled:!1,visible:!1,loading:!1,form:{enable:!0},rules:{name:[{required:!0,message:"请输入渠道名称",trigger:"blur"}],id:[{required:!0,message:"请输入渠道标识",trigger:"blur"}]},enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="新增",this.isDisabled=!1,this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.isDisabled=!0,this.visible=!0,this.$xloading.show(),e.next=7,o["f"].getDetail(t.id);case 7:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={enable:!0},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=16;break}if(e.$xloading.show(),a=Object.assign({},e.form),!e.isDisabled){t.next=11;break}return t.next=6,o["f"].editDeliverChannel(a);case 6:r=t.sent,e.$xloading.hide(),0==r.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+r.msg),t.next=16;break;case 11:return t.next=13,o["f"].addDeliverChannel(a);case 13:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg);case 16:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},c0c7:function(e,t,n){"use strict";n("1bdd")},c10a:function(e,t,n){"use strict";n("7a5c")},c641:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,"destroy-on-close":""},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"角色名称",prop:"roleName"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.form.roleName,callback:function(t){e.$set(e.form,"roleName",t)},expression:"form.roleName"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"status"}},[n("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),e._v(" "),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"排序",prop:"sort",required:""}},[n("el-input-number",{attrs:{"controls-position":"right",placeholder:""},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{type:"textarea",rows:2,placeholder:""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:20,offset:2}},[n("el-card",{scopedSlots:e._u([{key:"header",fn:function(){},proxy:!0}])},[e._v(" "),n("div",[n("el-form-item",{attrs:{label:"菜单权限",prop:"menuIds"}},[n("el-tree",{ref:"menuTree1",attrs:{data:e.menuTree1,"show-checkbox":"","check-strictly":"","node-key":"id",props:{label:"title",children:"children"}}})],1)],1)])],1)],1)],1)],1)},r=[],i=n("2909"),s=(n("96cf"),n("1da1")),o=n("3528"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}],sort:[{required:!0,message:"排序不能为空",trigger:"blur"}]},menuTree1:[]}},methods:{add:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.reset(),this.getMenuTreeData(),this.title="新增",this.visible=!0;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),edit:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,this.getMenuTreeData();case 6:return e.next=8,o["e"](t.id);case 8:n=e.sent,0==n.code&&(this.$xloading.hide(),this.form=n.data,void 0!=this.$refs.menuTree1&&this.$refs.menuTree1.setCheckedKeys(n.data.menuIds));case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.form.menuIds=Object(i["a"])(this.$refs.menuTree1.getCheckedKeys()),this.$refs["form"].validate(function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=e.form,r=e.form.id,void 0==r){t.next=12;break}return t.next=7,o["d"](a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["a"](a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getMenuTreeData:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["g"](0);case 2:t=e.sent,0==t.code&&(this.menuTree1=t.data);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},c66f:function(e,t,n){"use strict";n("3347")},c729:function(e,t,n){"use strict";n("1f2c")},c846:function(e,t,n){"use strict";n("f241")},c89d:function(e,t,n){"use strict";n("7fe1")},c961:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-refresh"},on:{click:e.handleStats}},[e._v("重新统计")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-class-name":e.highlightChildren},on:{"expand-change":e.handleExpandChange}},[e._v("\n      >\n      "),n("el-table-column",{attrs:{label:"日期",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"areaName",label:"地区",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"totalTarget",label:"目标",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueNewFans",label:"进线",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"changeNewFans",label:"调粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"bannedNewFans",label:"封号",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"residue",label:"剩余(容量)",sortable:"",align:"center","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"eequally",label:"公摊数",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"totalFans",label:"总粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueRepeatFans",label:"重复",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"重复率",align:"center",sortable:"","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.repeatFansPro)+"%")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"单价",align:"center","sortablemin-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.price)+" 元")])]}}])})],1)],1)],1)},r=[],i=(n("c5f6"),n("96cf"),n("1da1")),s=(n("ac6a"),n("5df3"),n("4f7f"),n("4624")),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{},expandedChildren:new Set}},created:function(){},methods:{queryReset:function(){this.queryParams={date:(new Date).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["i"].getAreaStatsList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getSummaries:function(e){var t=e.columns,n=e.data,a=[];return t.forEach((function(e,t){if(0!==t){var r=n.map((function(t){return Number(t[e.property])}));r.every((function(e){return isNaN(e)}))?a[t]="--":a[t]=r.reduce((function(e,t){return e+t}),0)}else a[t]="合计"})),a},handleExpandChange:function(e,t){var n=this;t?e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.add(e.id)})):e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.delete(e.id)}))},highlightChildren:function(e){var t=e.row;return this.expandedChildren.has(t.id)?"highlight-row":""},handleStats:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.queryParams.date,e.next=3,o["j"].stats(t);case 3:n=e.sent,0===n.code?this.$xMsgSuccess("重新统计成功"):this.$xMsgError(n.msg);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},c=l,u=(n("3743"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},ca1e:function(e,t,n){"use strict";n("3a7b")},cc1b:function(e,t,n){"use strict";n("4d27")},ccce:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:""},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"地区",prop:"areaId"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{url:"/deliverArea/options"},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"类别",prop:"cateId"}},[n("x-select",{attrs:{filterable:"",url:"/category/options"},on:{change:function(t){return e.handleCategoryChange(!0)}},model:{value:e.form.cateId,callback:function(t){e.$set(e.form,"cateId",t)},expression:"form.cateId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"模板",prop:"tId"}},[n("x-select",{attrs:{filterable:"",options:e.templateOptions},model:{value:e.form.tId,callback:function(t){e.$set(e.form,"tId",t)},expression:"form.tId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"问候文字",prop:"wsText"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:3},placeholder:"请输入问候携带的文字"},model:{value:e.form.wsText,callback:function(t){e.$set(e.form,"wsText",t)},expression:"form.wsText"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"渠道标识",prop:"cId"}},[n("x-select",{attrs:{filterable:"",url:"/deliverChannel/options"},model:{value:e.form.cId,callback:function(t){e.$set(e.form,"cId",t)},expression:"form.cId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"像素",prop:"pixelId"}},[n("el-input",{attrs:{placeholder:"请输入像素Id"},model:{value:e.form.pixelId,callback:function(t){e.$set(e.form,"pixelId",t)},expression:"form.pixelId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l=n("ed08"),c={data:function(){return{title:"",visible:!1,loading:!1,form:{title:Object(l["b"])(32),enable:!0},rules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],wsText:[{required:!0,message:"请输入问候文字",trigger:"blur"}],areaId:[{required:!0,message:"请选择地区",trigger:"blur"}],cateId:[{required:!0,message:"请选择类别",trigger:"blur"}],tId:[{required:!0,message:"请选择模板",trigger:"blur"}],cId:[{required:!0,message:"请输入渠道标识",trigger:"blur"}]},templateOptions:[],enableOptions:s["b"]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["z"].getDetail(t.id);case 6:if(n=e.sent,0!=n.code){e.next=11;break}return this.form=n.data,e.next=11,this.handleCategoryChange();case 11:this.$xloading.hide();case 12:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={title:Object(l["b"])(32),enable:!0},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["z"].editPage(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["z"].addPage(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleCategoryChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n,a=arguments;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]&&a[0],e.next=3,o["G"].getTemplateOptions({id:this.form.cateId});case 3:n=e.sent,0==n.code&&(this.templateOptions=n.data,t&&(this.form.tId=void 0));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},d0a2:function(e,t,n){"use strict";n("f559"),n("ac6a");var a={data:function(){return{tableHeight:0}},mounted:function(){var e=this,t=function(t){var n={queryName:"queryForm",toolbarName:"toolbar",page:!0,refs:[],value:0};t=Object.assign({},n,t);var a=50,r=40,i=2,s=40,o=0;e.$refs[t.queryName]&&(o=e.$refs[t.queryName].$el.offsetHeight);var l=0;e.$refs[t.toolbarName]&&(l=e.$refs[t.toolbarName].$el.offsetHeight+15);var c=0;t.page&&(c=32);var u=0;t.refs.forEach((function(t){var n="$el-";t.startsWith(n)?u+=e.$refs[t.substring(n.length)].$el.offsetHeight:u+=e.$refs[t].$el.offsetHeight}));var d=window.innerHeight-(a+r+s+i+o+l+c+1+t.value+u);e.tableHeight=d};window.onresize=function(){t(e.tableHeightOptions)},this.$nextTick((function(){t(e.tableHeightOptions)}))}};t["a"]=a},d4be:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannelUser:add"],expression:"['deliverChannelUser:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"id",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"userName",label:"渠道账号",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(n){return e.handleCustomerLogin(t.row.userName)}}},[n("div",[e._v(e._s(t.row.userName))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"nickName",label:"渠道名称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"enable",label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"addTime",label:"添加时间",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"editTime",label:"修改时间",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"260",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannelUser:resetPassword"],expression:"['deliverChannelUser:resetPassword']"}],attrs:{size:"mini"},on:{click:function(n){return e.handleResetPassword(t.row)}}},[e._v("重置密码")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannelUser:edit"],expression:"['deliverChannelUser:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["deliverChannelUser:delete"],expression:"['deliverChannelUser:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("70dd"),c={components:{EditDialog:l["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["g"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["g"].deldeliverChannelUser(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleResetPassword:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认重置该用户的密码","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["g"].resetPassword(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("重置密码成功")):n.$xMsgError("操作失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleCustomerLogin:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["g"].customerLogin(t);case 2:n=e.sent,0==n.code?(a="http://customer.729183.xyz/adminlogin?username=".concat(t,"&token=").concat(n.data),window.open(a)):this.$xMsgError("操作失败！"+n.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},db6a:function(e,t,n){"use strict";n("32d1")},dcd3:function(e,t,n){},de11:function(e,t,n){"use strict";n("a481"),n("386d"),n("4328"),n("83d6")},de28:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"800px"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-form-item",{attrs:{label:"新主号",prop:"accountText"}},[n("el-input",{attrs:{placeholder:"请输入新的主号字符串，多个使用换行分隔",clearable:"",type:"textarea",rows:8},model:{value:e.form.accountText,callback:function(t){e.$set(e.form,"accountText",t)},expression:"form.accountText"}},[n("template",{slot:"prepend"},[n("i",{staticClass:"el-icon-user"})])],2),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        如果是12位手机号码支持 ：************ 换 ************ 多个使用换行分隔\n      ")]),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        通用方法 ：************@************ 多个使用换行分隔\n      ")]),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        操作都是：************下线，************上线\n      ")])],1),e._v(" "),n("el-form-item",{attrs:{label:"失败列表"}},[n("el-input",{attrs:{placeholder:"失败列表",readonly:"readonly",clearable:"",type:"textarea",rows:5},model:{value:e.form.error,callback:function(t){e.$set(e.form,"error",t)},expression:"form.error"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"换号类型",prop:"changeAccountType"}},[n("x-radio",{attrs:{options:e.changeAccountOptions},model:{value:e.form.changeAccountType,callback:function(t){e.$set(e.form,"changeAccountType",t)},expression:"form.changeAccountType"}}),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        新号继承旧号进线数: 当新号的计数器是从旧号的进线数开始计数是选择这个。\n      ")]),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        新号不继承旧号进线数: 当新号的计数器是从0开始计数是选择这个。\n      ")])],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{title:"批量换号操作",visible:!1,loading:!1,currentRow:null,form:{taskId:null,accountText:"",changeAccountType:1},changeAccountOptions:[{label:"新号继承旧号进线数",value:1},{label:"新号不继承旧号进线数",value:2}],rules:{accountText:[{required:!0,message:"请输入新主号字符串",trigger:"blur"}]}}},methods:{open:function(e){this.currentRow=e,this.form.taskId=e.taskId,this.title="批量换号操作 - ".concat(this.currentRow.taskTitle),this.visible=!0,this.resetForm()},resetForm:function(){var e=this;this.form={taskId:this.currentRow?this.currentRow.taskId:null,accountText:"",changeAccountType:1},this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},submitForm:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=3;break}return t.next=3,e.handleChangeAccount();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleChangeAccount:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("确认将进行批量换号吗？","换号批量确认",{type:"warning",confirmButtonText:"确认批量换号",cancelButtonText:"取消"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.loading=!0,n={taskId:t.form.taskId,accountText:t.form.accountText,changeAccountType:t.form.changeAccountType},e.next=5,s["l"].betchChange(n);case 5:a=e.sent,0===a.code?(t.$xMsgSuccess("批量换号成功"),t.visible=!1,t.$emit("ok")):(t.$xMsgError("批量换号失败："+a.msg),t.form.error=a.data,t.$emit("ok")),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](0),t.$xMsgError("批量换号失败："+e.t0.message);case 12:return e.prev=12,t.loading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,9,12,15]])})))).catch((function(){}));case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},l=o,c=(n("de83"),n("2877")),u=Object(c["a"])(l,a,r,!1,null,"77e355c0",null);t["default"]=u.exports},de83:function(e,t,n){"use strict";n("dcd3")},df41:function(e,t,n){"use strict";n("2490")},e264:function(e,t,n){},e38b:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"","label-width":"80px"}},[n("el-row",{attrs:{gutter:15}},[n("el-form-item",{attrs:{label:"标题"}},[n("el-input",{model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"操作人员"}},[n("el-input",{model:{value:e.queryParams.operator,callback:function(t){e.$set(e.queryParams,"operator",t)},expression:"queryParams.operator"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"类型"}},[n("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.operateType,callback:function(t){e.$set(e.queryParams,"operateType",t)},expression:"queryParams.operateType"}},e._l(e.operateTypeOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"状态"}},[n("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"daterange",placeholder:"","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(){return e.queryParams={}}}},[e._v("重置")])],1)],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{prop:"logId",label:"ID",align:"center",width:"80"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"标题",align:"center",width:"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"operateType",label:"操作类型",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.operateType==e.operateType.Add?n("el-tag",{attrs:{type:"success"}},[e._v("新增")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Edit?n("el-tag",{attrs:{type:"warning"}},[e._v("修改")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Delete?n("el-tag",{attrs:{type:"danger"}},[e._v("删除")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Query?n("el-tag",[e._v("查询")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Unknown?n("el-tag",{attrs:{type:"info"}},[e._v("其它")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Login?n("el-tag",{attrs:{color:"#0070d3",effect:"dark"}},[e._v("登录")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Logout?n("el-tag",{attrs:{color:"#ec0810",effect:"dark"}},[e._v("注销")]):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"tableName",label:"表名",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.tableName?n("span",[e._v(e._s(t.row.tableName))]):n("span",[e._v(" - ")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"tableId",label:"ID",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.tableId?n("span",[e._v(e._s(t.row.tableId))]):n("span",[e._v(" - ")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"desc",label:"描述",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.desc?n("span",[e._v(e._s(t.row.desc))]):n("span",[e._v("-")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"ip",label:"IP",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(e._s(t.row.ip))]),e._v(" "),n("div",[e._v(e._s(t.row.ipLocation))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?n("el-tag",{attrs:{type:"success"}},[e._v("成功")]):"1"==t.row.status?n("el-tag",{attrs:{type:"warning"}},[e._v("失败")]):n("el-tag",{attrs:{type:"danger"}},[e._v("异常")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"operatorName",label:"操作人员",align:"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(e._s(t.row.operatorName))]),e._v(" "),n("div",[e._v("ID: "+e._s(t.row.operatorId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"logTime",label:"操作时间",align:"center",width:"180"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return e.handleView(t.row)}}},[e._v("详细")])]}}])})],1)],1),e._v(" "),n("detail-dialog",{ref:"detailDialog"})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("60fe"),l=n("55cb"),c=n("a457"),u={mixins:[s["a"]],components:{DetailDialog:l["default"]},data:function(){return{searchCol:{xs:24,md:8},queryParams:{},loading:!1,tableData:{},operateTypeOptions:[{label:"新增",value:1},{label:"修改",value:2},{label:"删除",value:3},{label:"查询",value:4},{label:"注销",value:5},{label:"登录",value:6},{label:"其它",value:9}],statusOptions:[{label:"成功",value:"0"},{label:"失败",value:"1"},{label:"异常",value:"2"}],operateType:o["OperateType"]}},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.date&&(t.beginTime=t.date[0],t.endTime=t.date[1]),e.next=5,c["b"](t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),renderOperateType:function(e){var t=e.operateType,n="未知";switch(t){case"1":n="新增";break;case"2":n="修改";break;case"3":n="删除";break;case"4":n="查询";break;case"5":n="退出登录";break;default:}return n},handleView:function(e){this.$refs.detailDialog.show(e)}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},e702:function(e,t,n){},e722:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-steps",{attrs:{active:e.activeStep,"finish-status":"success"}},[n("el-step",{attrs:{title:"任务信息"}}),e._v(" "),n("el-step",{attrs:{title:"渠道分配"}}),e._v(" "),n("el-step",{attrs:{title:"完成"}})],1),e._v(" "),n("el-form",{ref:"formRef",staticStyle:{"margin-top":"20px"},attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[n("el-row",[0===e.activeStep?n("div",[n("div",{staticClass:"form-section"},[n("h3",{staticClass:"section-title"},[e._v("任务信息")]),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"任务Id",prop:"id"}},[n("el-input",{attrs:{disabled:"false"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[n("el-input",{model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"任务容量",prop:"target"}},[n("el-input-number",{attrs:{placeholder:""},model:{value:e.form.target,callback:function(t){e.$set(e.form,"target",t)},expression:"form.target"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"单主号容量",prop:"singleAccountTarget"}},[n("el-input-number",{attrs:{placeholder:""},model:{value:e.form.singleAccountTarget,callback:function(t){e.$set(e.form,"singleAccountTarget",t)},expression:"form.singleAccountTarget"}})],1)],1)],1),e._v(" "),n("div",{staticClass:"form-section"},[n("h3",{staticClass:"section-title"},[e._v("地区配置")]),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"投放地区",prop:"areaId"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{url:"/deliverArea/options"},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"时区",prop:"utc"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{options:e.utcTimeZoneOptions},model:{value:e.form.utc,callback:function(t){e.$set(e.form,"utc",t)},expression:"form.utc"}})],1)],1)],1),e._v(" "),n("div",{staticClass:"form-section"},[n("h3",{staticClass:"section-title"},[e._v("其他配置")]),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"粉丝单价",prop:"price"}},[n("el-input-number",{staticStyle:{width:"30%"},attrs:{placeholder:""},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}}),e._v("￥\n              ")],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("x-radio",{attrs:{button:"",options:e.enableOptions},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"当日结单时间",prop:"dailyStatementTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择时间",format:"yyyy-MM-dd HH:mm","value-format":"yyyy-MM-dd HH:mm"},model:{value:e.form.dailyStatementTime,callback:function(t){e.$set(e.form,"dailyStatementTime",t)},expression:"form.dailyStatementTime"}})],1)],1)],1),e._v(" "),n("div",{staticClass:"form-section"},[n("h3",{staticClass:"section-title"},[e._v("计数器配置")]),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"计数器",prop:"counterOrderId"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{filterable:!0,url:"/counterOrderConfig/options"},on:{change:e.handleCounterOrderChange},model:{value:e.form.counterOrderId,callback:function(t){e.$set(e.form,"counterOrderId",t)},expression:"form.counterOrderId"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"计数器Url",prop:"counterOrderUrl"}},[n("el-input",{attrs:{placeholder:"请输入计数器Url"},model:{value:e.form.counterOrderUrl,callback:function(t){e.$set(e.form,"counterOrderUrl",t)},expression:"form.counterOrderUrl"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"计数器账号",prop:"counterOrderAccount"}},[n("el-input",{attrs:{placeholder:"请输入计数器密码"},model:{value:e.form.counterOrderAccount,callback:function(t){e.$set(e.form,"counterOrderAccount",t)},expression:"form.counterOrderAccount"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"计数器密码",prop:"counterOrderPassWord"}},[n("el-input",{attrs:{placeholder:"请输入计数器密码"},model:{value:e.form.counterOrderPassWord,callback:function(t){e.$set(e.form,"counterOrderPassWord",t)},expression:"form.counterOrderPassWord"}})],1)],1)],1),e._v(" "),n("div",{staticClass:"form-section"},[n("h3",{staticClass:"section-title"},[e._v("渠道和主号配置")]),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"渠道",prop:"channelIds"}},[n("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{"fetch-suggestions":e.querySearch,placeholder:"请输入需要查询的渠道标识","trigger-on-focus":!1,debounce:50,"prefix-icon":"el-icon-search"},on:{select:e.handleSelect},model:{value:e.queryCid,callback:function(t){e.queryCid=t},expression:"queryCid"}}),e._v(" "),n("el-input",{attrs:{type:"textarea",autosize:{minRows:15},placeholder:"请输入渠道标识,多个换行分隔"},model:{value:e.form.channelIds,callback:function(t){e.$set(e.form,"channelIds",t)},expression:"form.channelIds"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{"margin-left":"110px",display:"flex","align-items":"center","margin-bottom":"10px"}},[n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.getOrderAccounts}},[e._v("获取当日全部主号")]),e._v(" "),n("span",{staticStyle:{"font-size":"12px",color:"#666","margin-left":"10px"}},[e._v("\n                  获取当日所有已抓取的主号账户，每日"),n("b",{staticStyle:{color:"green"}},[e._v("任务投放中")]),e._v("更新主号时候使用这个，不会导致下线主号数据丢失\n                ")])],1),e._v(" "),n("div",{staticStyle:{"margin-left":"110px",display:"flex","align-items":"center"}},[n("el-button",{attrs:{type:"warning",size:"mini"},on:{click:e.getOnlineAccounts}},[e._v("获取当日在线主号")]),e._v(" "),n("span",{staticStyle:{"font-size":"12px",color:"#666","margin-left":"10px"}},[e._v("\n                  获取当日在线状态的主号账户，每日"),n("b",{staticStyle:{color:"red"}},[e._v("任务投放结束")]),e._v("分配工单的时候使用这个，只获取最新的主号分配，"),n("b",{staticStyle:{color:"red"}},[e._v("会删除下线账号数据")])])],1),e._v(" "),n("el-form-item",{staticStyle:{"margin-top":"30px"},attrs:{label:"主号",prop:"accountIds"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:15},placeholder:"请输入主号,多个换行分隔"},model:{value:e.form.accountIds,callback:function(t){e.$set(e.form,"accountIds",t)},expression:"form.accountIds"}})],1)],1)],1)]):e._e()]),e._v(" "),1===e.activeStep?n("div",[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"渠道绑定"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择渠道"},on:{change:e.updateAvailableAccounts},model:{value:e.selectedChannel,callback:function(t){e.selectedChannel=t},expression:"selectedChannel"}},e._l(e.availableChannels,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"账号绑定"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"选择主号"},model:{value:e.selectedAccounts,callback:function(t){e.selectedAccounts=t},expression:"selectedAccounts"}},e._l(e.availableAccounts,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),e._v(" "),n("el-button",{staticStyle:{"margin-top":"10px","margin-left":"30px"},attrs:{type:"primary"},on:{click:e.bindAccounts}},[e._v("绑定")]),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-table",{staticStyle:{"margin-top":"20px","margin-bottom":"20px"},attrs:{data:e.bindings,border:""}},[n("el-table-column",{attrs:{prop:"channel",label:"渠道",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"accounts",label:"绑定主号",align:"center","min-width":"500"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v("\n                  "+e._s(n.accounts.join(", "))+"\n                ")]}}],null,!1,**********)}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.bind(a.channel)}}},[e._v("添加")]),e._v(" "),n("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(t){return e.unbind(a.channel)}}},[e._v("解绑")])]}}],null,!1,**********)})],1)],1)],1)],1):e._e(),e._v(" "),2===e.activeStep?n("div",[n("el-alert",{attrs:{title:"请确认您的信息",type:"info","show-icon":""}}),e._v(" "),n("p",[n("strong",[e._v("任务名称：")]),e._v(e._s(e.form.name))]),e._v(" "),n("p",[n("strong",[e._v("目标：")]),e._v(e._s(e.form.target))]),e._v(" "),n("p",[n("strong",[e._v("计数器：")]),e._v(e._s(e.form.counterOrderUrl))]),e._v(" "),n("p",[n("strong",[e._v("绑定关系：")])]),e._v(" "),n("ul",e._l(e.bindings,(function(t){return n("li",{key:t.channel},[n("strong",[e._v(e._s(t.channel)+"：")]),e._v(" "+e._s(t.accounts.join(", "))+"\n          ")])})),0)],1):e._e(),e._v(" "),n("el-form-item",[e.activeStep>0?n("el-button",{on:{click:e.prevStep}},[e._v("上一步")]):e._e(),e._v(" "),e.activeStep<2?n("el-button",{attrs:{type:"primary"},on:{click:e.nextStep}},[e._v("下一步")]):e._e(),e._v(" "),2===e.activeStep?n("el-button",{attrs:{type:"success"},on:{click:e.submitForm}},[e._v("提交")]):e._e()],1)],1)],1)],1)},r=[],i=(n("8e6e"),n("456d"),n("7f7f"),n("6762"),n("2fdb"),n("7514"),n("ade3")),s=n("2909"),o=(n("ac6a"),n("5df3"),n("4f7f"),n("28a5"),n("96cf"),n("1da1")),l=n("3620"),c=n("365c");function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){Object(i["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m={data:function(){return{operation:0,activeStep:0,form:{enable:!0,state:0,name:"",utc:8,channelIds:"",accountIds:"",price:12,singleAccountTarget:20},rules:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],utc:[{required:!0,message:"请输入时区",trigger:"blur"}],target:[{required:!0,message:"请输入容量",trigger:"blur"}],channelIds:[{required:!0,message:"请输入投放渠道标识",trigger:"blur"}],accountIds:[{required:!0,message:"请输入投放主号",trigger:"blur"}],price:[{required:!0,message:"请输入粉丝单价",trigger:"blur"}],dailyStatementTime:[{required:!0,message:"请选择每日结单时间",trigger:"blur"}]},selectedChannel:"",selectedAccounts:[],bindings:[],utcTimeZoneOptions:l["d"],enableOptions:l["b"],channelOptions:[],queryCid:"",statusOptions:[{label:"禁止渠道用户修改",value:0},{label:"允许渠道用户修改",value:1}]}},created:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.$router.history.current.query,t.operation&&(this.operation=t.operation),!t.id){e.next=5;break}return e.next=5,this.getDetail(t.id);case 5:return e.next=7,Object(l["c"])();case 7:this.channelOptions=e.sent;case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),computed:{channelList:function(){return this.form.channelIds.split("\n").filter((function(e){return e.trim()}))},accountList:function(){return this.form.accountIds.split("\n").filter((function(e){return/^[0-9]+$/.test(e.trim())}))},availableAccounts:function(){var e=new Set(this.bindings.flatMap((function(e){return e.accounts||[]})));return Object(s["a"])(new Set(this.accountList)).filter((function(t){return!e.has(t)}))},availableChannels:function(){var e=new Set(this.bindings.flatMap((function(e){return e.channel||[]})));return Object(s["a"])(new Set(this.channelList)).filter((function(t){return!e.has(t)}))},accountIds:function(){return this.accountList.join("\n")}},watch:{accountList:function(){this.cleanBindings()}},methods:{getDetail:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,c["j"].getDetail(t);case 3:n=e.sent,0==n.code&&(this.form=n.data,this.bindings=n.data.bindings,9==this.operation&&(this.form.id=null)),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),cleanBindings:function(){var e=new Set(this.accountList);this.bindings=this.bindings.map((function(t){return d(d({},t),{},{accounts:t.accounts.filter((function(t){return e.has(t)}))})})).filter((function(e){return e.accounts.length>0}))},updateAvailableAccounts:function(){this.selectedAccounts=[]},bindAccounts:function(){this.selectedChannel&&0!==this.selectedAccounts.length&&(this.bindings.push({channel:this.selectedChannel,accounts:Object(s["a"])(this.selectedAccounts)}),this.selectedChannel="",this.selectedAccounts=[])},bind:function(e){var t=this.bindings.find((function(t){return t.channel===e}));t?t.accounts=[].concat(Object(s["a"])(t.accounts),Object(s["a"])(this.selectedAccounts)):this.bindings.push({channel:e,accounts:Object(s["a"])(this.selectedAccounts)}),this.selectedAccounts=[]},unbind:function(e){this.bindings=this.bindings.filter((function(t){return t.channel!==e}))},prevStep:function(){this.activeStep>0&&this.activeStep--},nextStep:function(){var e=this;this.$refs.formRef.validate((function(t){t&&e.activeStep++}))},close:function(){this.$router.push({path:"/deliver/delivertask"})},submitForm:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.form.bindings=this.bindings,this.form.accountIds=this.accountIds,t=this.form,1!=this.operation){e.next=11;break}return e.next=6,c["j"].editDeliverTask(t);case 6:n=e.sent,this.$xloading.hide(),0==n.code?(this.$xMsgSuccess("修改成功"),this.close()):this.$xMsgError("操作失败！"+n.msg),e.next=16;break;case 11:return e.next=13,c["j"].addDeliverTask(t);case 13:a=e.sent,this.$xloading.hide(),0==a.code?(this.$xMsgSuccess("添加成功"),this.close()):this.$xMsgError("操作失败！"+a.msg);case 16:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),querySearch:function(e,t){var n=this.channelOptions,a=e?n.filter((function(t){return t.value.toLowerCase().indexOf(e.toLowerCase())>-1||t.label.toLowerCase().indexOf(e.toLowerCase())>-1})):n;a=a.map((function(e){return{value:e.label+" 【"+e.value+"】",name:e.value}})),t(a)},handleSelect:function(e){var t=this.form.channelIds.split("\n").filter((function(e){return e.trim()}));t.includes(e.name)||(t.push(e.name),this.form.channelIds=t.join("\n")),this.queryCid=""},handleCounterOrderChange:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.form.counterOrderId,e.next=3,c["c"].getDetail(t);case 3:n=e.sent,0==n.code&&(this.$set(this.form,"counterOrderUrl",n.data.url||""),this.$set(this.form,"counterOrderAccount",n.data.account||""),this.$set(this.form,"counterOrderPassWord",n.data.passWord||""));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getOrderAccounts:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.form.counterOrderId,t&&-1!=t){e.next=3;break}return e.abrupt("return",this.$xMsgError("请先选择计数器！"));case 3:return n={CounterOrderConfigId:t},e.next=6,c["d"].getAccounts(n);case 6:a=e.sent,0==a.code&&(this.form.accountIds=a.data.join("\n"));case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getOnlineAccounts:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.form.counterOrderId,t&&-1!=t){e.next=3;break}return e.abrupt("return",this.$xMsgError("请先选择计数器！"));case 3:return n={CounterOrderConfigId:t},e.next=6,c["d"].getOnlineAccounts(n);case 6:a=e.sent,0==a.code&&(this.form.accountIds=a.data.join("\n"));case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},p=m,f=(n("1049"),n("2877")),h=Object(f["a"])(p,a,r,!1,null,"70bf16a8",null);t["default"]=h.exports},e985:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"主号"}},[n("el-input",{attrs:{placeholder:"请输入用户主号"},model:{value:e.queryParams.account,callback:function(t){e.$set(e.queryParams,"account",t)},expression:"queryParams.account"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"工单标题"}},[n("el-input",{attrs:{placeholder:"请输入工单标题"},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.createTime,callback:function(t){e.$set(e.queryParams,"createTime",t)},expression:"queryParams.createTime"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"date",label:"日期",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"头像",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("el-image",{staticStyle:{width:"35px",height:"35px"},attrs:{fit:"contain",src:t.headImg,"preview-src-list":[t.headImg]}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"account",label:"主号",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"nickName",label:"昵称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"seatName",label:"席位",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"title",label:"工单",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"platform",label:"平台",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"在线状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.isOnline?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("在线")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("离线")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"分配状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.isAllocation?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("开启")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("关闭")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"已完成/当日目标",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.dayNewFans)+" / "+e._s(a.singleDayTarget))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"已完成/总目标",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.newFans)+" / "+e._s(a.totalTarget))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"dayRepeatFans",label:"本工单当日重粉数",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"repeatFans",label:"本工单重粉数",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"lastOfflineTime",label:"上次离线时间",align:"center","min-width":"200"}})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{account:null,createTime:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{}}},created:function(){var e=this.$router.history.current.query;e.account&&(this.queryParams.account=e.account)},methods:{queryReset:function(){this.queryParams={createTime:new Date(Date.UTC((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate())).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.createTime&&(t.date=t.createTime,delete t.createTime),e.next=5,o["d"].getList(t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},ea9a:function(e,t,n){},eb93:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"工单标题"}},[n("el-input",{model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"工单Url"}},[n("el-input",{model:{value:e.queryParams.url,callback:function(t){e.$set(e.queryParams,"url",t)},expression:"queryParams.url"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"工单状态"}},[n("x-select",{attrs:{"show-default":"",url:"/options/getCounterOrderConfigStateTypes"},model:{value:e.queryParams.state,callback:function(t){e.$set(e.queryParams,"state",t)},expression:"queryParams.state"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["counterOrderConfig:add"],expression:"['counterOrderConfig:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["counterOrderConfig:add"],expression:"['counterOrderConfig:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleBatchAdd}},[e._v("批量新增")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"title",label:"标题",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"platform",label:"平台",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"url",label:"工单URL/UUID",align:"center","min-width":"700"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.url))]),n("br"),e._v(" "),n("span",[e._v(e._s(a.uuId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"启用状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.enable?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("启用")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"任务状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.state?n("el-tag",{attrs:{size:"medium"}},[e._v("等待抓取")]):1==a.state?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("抓取中")]):9==a.state?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("抓取错误")]):99==a.state?n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("订单过期")]):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"lastStartSpiderTime",label:"添加时间/过期时间",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.createOn))]),n("br"),e._v(" "),n("span",[e._v(e._s(a.expireTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"lastStartSpiderTime",label:"抓取时间/完成时间",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.lastStartSpiderTime))]),n("br"),e._v(" "),n("span",[e._v(e._s(a.lastFinishSpiderTime))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"errorMsg",label:"错误信息",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["counterOrderConfig:edit"],expression:"['counterOrderConfig:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["counterOrderConfig:delete"],expression:"['counterOrderConfig:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}}),e._v(" "),n("batch-dialog",{ref:"batchDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l=n("2141"),c=n("83d6a"),u={components:{EditDialog:l["default"],BatchDialog:c["default"]},mixins:[s["a"]],data:function(){return{queryParams:{state:-1},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={state:-1},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["c"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleBatchAdd:function(){this.$refs.batchDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["c"].delCounterOrderConfig(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},ecd0:function(e,t,n){},f051:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[n("el-form-item",[n("el-form-item",{attrs:{label:"所属用户"}},[n("x-select",{attrs:{"show-default":"",url:"/fBUser/options"},model:{value:e.queryParams.fbUserId,callback:function(t){e.$set(e.queryParams,"fbUserId",t)},expression:"queryParams.fbUserId"}})],1),e._v(" "),n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-user"},on:{click:e.handleSyncUserData}},[e._v("同步账户")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{label:"状态",align:"center","min-width":"100",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.disableReason?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("正常")]):n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("禁用")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账号",align:"center","min-width":"200",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[n("el-link",{attrs:{type:"primary",target:"_blank"},on:{click:function(t){return e.handleFBAdManager(a)}}},[e._v("\n              "+e._s(a.name)+"\n            ")])],1),e._v(" "),n("br"),e._v(" "),n("span",[e._v(e._s(a.accountId))]),e._v(" "),n("br"),e._v(" "),n("el-link",{attrs:{type:"primary",target:"_blank"},on:{click:function(t){return e.handleStats(a)}}},[e._v(" 每日数据 ")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"管理员",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.adminUserCount))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账号类型",align:"center","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.fbBusinessId?n("span",[e._v("Business")]):n("span",[e._v("Personal normal")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账单金额",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s((a.balance/1).toFixed(2))+" "+e._s(a.currency))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"门槛",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s((a.thresholdAmount/100).toFixed(2))+" "+e._s(a.currency))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"日限额",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.adtrustDsl.toFixed(2))+" "+e._s(a.currency))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"总花费",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s((a.amountSpent/100).toFixed(2))+" "+e._s(a.currency))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"花费限额",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.spendCap?n("div",[n("span",[e._v("-")])]):n("div",[n("span",[e._v(e._s((a.spendCap/100).toFixed(2))+" "+e._s(a.currency))])])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"currency",label:"币种",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"账户类型",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.isPrepayAccount?n("span",[e._v("预付费")]):n("span",[e._v("后付费")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"所有者角色",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["Admin"==a.userRole?n("el-tag",{attrs:{size:"medium"}},[e._v(e._s(a.userRole))]):n("el-tag",{attrs:{type:"info",size:"medium"}},[e._v(e._s(a.userRole))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"支付方法",align:"center","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",{domProps:{innerHTML:e._s(e.allPaymentFormat(a.allPaymentMethodsPmCreditCard))}})]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"账单期",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.nextBillDateStr))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"锁定原因",align:"center","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[0==a.disableReason?n("div",[n("span",[e._v("-")])]):n("div",[n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v(e._s(a.disableReasonStr))])],1)]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"createDate",label:"创建日期",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"时区",align:"center","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.timezoneName)+" | "+e._s(a.timezoneOffsetHoursUtc))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"fbBusinessId",label:"创建自BM",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"businessCountryCode",label:"国家编码",align:"center","min-width":"150"}})],1)],1)],1)},r=[],i=(n("ac6a"),n("28a5"),n("96cf"),n("1da1")),s=n("3fa5"),o=n("5c96"),l=n("4624"),c=n("365c"),u={components:{},mixins:[l["a"]],data:function(){return{queryParams:{fbUserId:-1},loading:!1,tableData:{},syncLock:!1}},mounted:function(){this.listenerMessage()},beforeDestroy:function(){window.removeEventListener("message",this.handleMessage)},methods:{queryReset:function(){this.queryParams={fbUserId:-1},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,c["p"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,c["p"].delFBAdAccount(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},allPaymentFormat:function(e){var t=e.split("|"),n="";return t.forEach((function(e){n+=e+"<br/>"})),n=n.slice(0,-5),n},handleFBAdManager:function(e){var t="https://adsmanager.facebook.com/adsmanager/manage/campaigns?act=".concat(e.accountId);window.open(t)},handleStats:function(e){this.$router.push({path:"/stats/adinsights",query:{account_id:e.accountId}})},handleSyncUserData:function(){this.syncLock?this.$xMsgWarning("账户正在同步中，请不要重复操作！"):(this.syncLock=!0,this.$xMsgInfo("账户开始同步，请等候同步通知！"),s["a"]({type:"SyncUserData"}))},listenerMessage:function(){var e=this;window.addEventListener("message",(function(t){if("SyncUserDataResponse"!==t.data.type);else{e.syncLock=!1;var n=t.data.data;1==n.code&&Object(o["Notification"])({title:"账户同步成功",message:"账户同步成功",type:"success",duration:0,position:"bottom-right",dangerouslyUseHTMLString:!0})}}),!1)}}},d=u,m=n("2877"),p=Object(m["a"])(d,a,r,!1,null,null,null);t["default"]=p.exports},f1de:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"用户Id",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"用户昵称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"type",label:"账户类型",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["User"==a.type?n("el-tag",[e._v("用户账户")]):e._e(),e._v(" "),"Public"==a.type?n("el-tag",{attrs:{type:"info"}},[e._v("公共主页账户")]):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"抓取时间",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"lastUpdateTime",label:"最后更新时间",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"授权状态",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[1==a.oAuthState?n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("已授权")]):0==a.oAuthState?n("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("未授权")]):9==a.oAuthState?n("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("授权过期")]):e._e()]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"oAuthExpiresTime",label:"授权过期时间",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["fBUser:oauth"],expression:"['fBUser:oauth']"}],attrs:{size:"mini",type:"primary"},on:{click:function(n){return e.handleOAuth(t.row)}}},[e._v("授权")])]}}])})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["u"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["u"].delFBUser(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleOAuth:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,o["u"].getOAthUrl(t.id);case 3:n=e.sent,0==n.code?window.open(n.data):this.$xMsgError("删除失败！"+n.msg),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},f241:function(e,t,n){},f473:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"地区Id",prop:"id"}},[n("el-input",{attrs:{disabled:!0,placeholder:"请输入地区Id"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"地区名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入地区名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"时区",prop:"timeZone"}},[n("x-select",{staticStyle:{width:"100%"},attrs:{options:e.utcTimeZoneOptions},model:{value:e.form.timeZone,callback:function(t){e.$set(e.form,"timeZone",t)},expression:"form.timeZone"}})],1)],1),e._v(" "),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"排序",prop:"sort"}},[n("el-input",{attrs:{type:"number",placeholder:"请输入排序"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1)],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("3620"),o=n("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{name:[{required:!0,message:"请输入地区名称",trigger:"blur"}],timeZone:[{required:!0,message:"请输入时区",trigger:"blur"}],sort:[{required:!0,message:"请输入排序",trigger:"blur"}]},utcTimeZoneOptions:s["d"]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,o["e"].getDetail(t.id);case 6:n=e.sent,0==n.code&&(this.form=n.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a,r,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=17;break}if(e.$xloading.show(),a=Object.assign({},e.form),r=a.id,void 0==r){t.next=12;break}return t.next=7,o["e"].editDeliverArea(a);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,o["e"].addDeliverArea(a);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},f56f:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[n("el-card",[n("div",{attrs:{slot:"header"},slot:"header"},[e._v("清理缓存")]),e._v(" "),n("div",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px",size:"medium"}},[n("el-row",[n("el-form-item",{attrs:{label:"key",prop:"key"}},[n("el-input",{attrs:{placeholder:"请输入key清理缓存",clearable:""},model:{value:e.form.key,callback:function(t){e.$set(e.form,"key","string"===typeof t?t.trim():t)},expression:"form.key"}}),e._v(" "),n("span",{staticClass:"extra"},[e._v("注意：不需要输入key的前缀；支持批量删除，当key以*结尾时（如xxx*），将删除所有以xxx开头的Key")])],1),e._v(" "),e.historyKeys.length>0?n("div",{staticClass:"history"},[n("span",{staticClass:"text"},[e._v("历史记录：")]),e._v(" "),e._l(e.historyKeys,(function(t,a){return n("el-tag",{key:a,staticClass:"item",attrs:{size:"mini",closable:""},on:{close:function(t){return e.onTagClose(a)},click:function(n){return e.onTagClick(t)}}},[e._v(e._s(t))])}))],2):e._e(),e._v(" "),n("el-form-item",{attrs:{label:" "}},[n("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("清理缓存")])],1)],1)],1)],1)])],1)},r=[],i=(n("20d6"),n("6762"),n("2fdb"),n("96cf"),n("1da1")),s=n("2b0e"),o=n("365c"),l={data:function(){return{loading:!1,form:{},rules:{key:[{required:!0,message:"请输入key"}]},historyKeys:[]}},mounted:function(){this.reload()},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=7;break}return e.$xloading.show(),t.next=4,o["D"].deleteByKey(e.form.key);case 4:a=t.sent,e.$xloading.hide(),0==a.code?(e.$xMsgSuccess("删除成功"),e.saveKeyHistory(e.form.key)):e.$xMsgError("删除失败！".concat(a.msg));case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleReset:function(){this.form={}},reload:function(){this.historyKeys=this.getHistory()||[]},saveKeyHistory:function(e){if(e){var t=this.getHistory();if(t)if(t.includes(e)){var n=t.findIndex((function(t){return t==e}));t.splice(n,1),t.unshift(e),this.setHistory(t)}else 10==t.length&&t.splice(t.length-1,1),t.unshift(e),this.setHistory(t);else this.setHistory([e])}},setHistory:function(e){s["default"].ls.set("redis_key_history",e),this.historyKeys=e},getHistory:function(){var e=s["default"].ls.get("redis_key_history");return e},onTagClose:function(e){var t=this.getHistory();t.splice(e,1),this.setHistory(t)},onTagClick:function(e){this.$set(this.form,"key",e)}}},c=l,u=(n("00d8"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,"39dc9adc",null);t["default"]=d.exports},f58d:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{staticStyle:{"margin-bottom":"10px"}},e._l(e.alertInfoList,(function(t){return n("el-alert",{key:t.id,staticStyle:{"margin-bottom":"10px"},attrs:{title:t.title,type:t.type},on:{close:function(n){return e.handleAlertClose(t)}}})})),1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"header-cell-style":{color:"#34495e",fontWeight:"bold",fontSize:"15px",padding:"14px 0",letterSpacing:"1px"},"cell-style":{fontSize:"14px",padding:"5px 0",background:"#fff"},"row-class-name":e.tableRowClassName,border:"",stripe:""}},e._l(e.columns,(function(t,a){return n("el-table-column",{key:a,attrs:{prop:t.prop,label:t.label,align:"center","min-width":t.width},scopedSlots:e._u([{key:"default",fn:function(a){return[e.isChannelColumn(t.prop)&&null!=a.row[""+t.prop]?n("div",{staticClass:"channel-column"},[n("div",{staticClass:"column-item"},[n("span",{staticClass:"label"},[e._v("进线：")]),e._v(" "),n("span",{staticClass:"value"},[e._v("\n                "+e._s(a.row[""+t.prop])+"\n              ")])]),e._v(" "),n("div",{staticClass:"column-item"},[n("span",{staticClass:"label"},[e._v("容量：")]),e._v(" "),n("span",{staticClass:"value"},[e._v(e._s(a.row[""+t.prop.replace("channel","residue")]))])]),e._v(" "),n("div",{staticClass:"column-item"},[n("span",{staticClass:"label"},[e._v("账号：")]),e._v(" "),n("span",{staticClass:"value"},[e._v(e._s(a.row[""+t.prop.replace("channel","accounts")]))])]),e._v(" "),n("div",{staticClass:"column-item"},[n("span",{staticClass:"label"},[e._v("调粉：")]),e._v(" "),n("span",{staticClass:"value"},[e._v(e._s(a.row[""+t.prop.replace("channel","changeNum")]))])]),e._v(" "),n("div",{staticClass:"column-item"},[n("span",{staticClass:"label"},[e._v("封号：")]),e._v(" "),n("span",{staticClass:"value"},[e._v(e._s(a.row[""+t.prop.replace("channel","bannedNum")]))])])]):"name"===t.prop&&"合计"!=a.row[t.prop]?n("task-info",{staticClass:"task-info",attrs:{taskId:a.row["id"],taskName:a.row[t.prop],areaName:a.row["areaName"]}}):"price"===t.prop&&"--"!=a.row[t.prop]?n("span",{staticClass:"price-column"},[n("el-tag",{attrs:{effect:"dark",size:"small"}},[e._v("¥"+e._s(a.row[t.prop]))])],1):"repeatFansPro"===t.prop?n("span",{staticClass:"percent-column"},[n("span",{staticStyle:{"margin-left":"8px"}},[e._v(e._s(a.row[t.prop])+"%")])]):"合计"==a.row[t.prop]?n("span",{staticClass:"total-row"},[n("el-icon",{staticStyle:{"vertical-align":"middle",color:"#f56c6c"}},[n("i",{staticClass:"el-icon-s-flag"})]),e._v(" "),n("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(a.row[t.prop]))])],1):"newFans"==t.prop?n("span",[e._v("\n            "+e._s(a.row[t.prop])+"\n            "),a.row.orderDailyNewFans?n("span",[n("br"),e._v(" "),n("el-tag",{attrs:{effect:"dark",size:"small"}},[e._v(e._s(a.row.orderDailyNewFans))])],1):e._e()]):n("span",{staticClass:"normal-cell"},[e._v("\n            "+e._s(a.row[t.prop])+"\n          ")])]}}],null,!0)})})),1)],1),e._v(" "),n("countdown",{ref:"timer",attrs:{table:e.$refs.table}})],1)},r=[],i=(n("7f7f"),n("6762"),n("2fdb"),n("20d6"),n("96cf"),n("1da1")),s=n("8046"),o=n("809c"),l=n("4624"),c=n("365c"),u={components:{TaskInfo:o["a"],Countdown:s["a"]},mixins:[l["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},columns:[],alertInfoList:[],fetchNotificationsTimer:null}},mounted:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.fetchNotifications(),this.fetchNotificationsTimer=setInterval((function(){t.fetchNotifications()}),6e4);case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),beforeUnmount:function(){clearInterval(this.fetchNotificationsTimer)},beforeRouteLeave:function(e,t,n){clearInterval(this.fetchNotificationsTimer),this.$refs.timer.stopAutoRefresh(),n()},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getColumns:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,c["i"].getTaskStatColumns();case 3:t=e.sent,t&&0==t.code?this.columns=t.data:this.$xMsgError(t.msg),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),fetchNotifications:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c["F"].getNoticeInfo();case 3:if(t=e.sent,0===t.code){e.next=6;break}return e.abrupt("return");case 6:this.$set(this,"alertInfoList",t.data.alertList||[]),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](0),console.error("获取通知失败:",e.t0);case 12:case"end":return e.stop()}}),e,this,[[0,9]])})));function t(){return e.apply(this,arguments)}return t}(),getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,c["i"].getTaskStatsList(t);case 4:n=e.sent,this.columns=n.columns,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAlertClose:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c["F"].readNotice(t.id,3);case 3:n=this.alertInfoList.findIndex((function(e){return e.id===t.id})),n>-1&&this.alertInfoList.splice(n,1),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),console.error("关闭通知失败:",e.t0);case 10:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(t){return e.apply(this,arguments)}return t}(),isChannelColumn:function(e){return e.includes("-channel")},tableRowClassName:function(e){var t=e.row;return"合计"===t.name?"total-row-class":""}}},d=u,m=(n("df41"),n("2877")),p=Object(m["a"])(d,a,r,!1,null,"7df17ab1",null);t["default"]=p.exports},f5e8:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"","label-width":"80px"}},[n("el-row",{attrs:{gutter:15}},[n("el-form-item",{attrs:{label:"登录账号"}},[n("el-input",{model:{value:e.queryParams.loginName,callback:function(t){e.$set(e.queryParams,"loginName",t)},expression:"queryParams.loginName"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"状态"}},[n("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"daterange",placeholder:"","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(){return e.queryParams={}}}},[e._v("重置")])],1)],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{prop:"logId",label:"ID",align:"center",width:"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"loginName",label:"登录账号",align:"center",width:"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"realName",label:"姓名",align:"center",width:"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"ipAddr",label:"登录IP",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(e._s(t.row.ipAddr))]),e._v(" "),n("div",[e._v(e._s(t.row.ipLocation))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"status",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?n("el-tag",{attrs:{type:"success"}},[e._v("成功")]):"1"==t.row.status?n("el-tag",{attrs:{type:"warning"}},[e._v("失败")]):n("el-tag",{attrs:{type:"danger"}},[e._v("异常")])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"msg",label:"操作信息",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{prop:"loginTime",label:"登录时间",align:"center",width:"180"}})],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("a457"),l={mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},statusOptions:[{label:"成功",value:"0"},{label:"失败",value:"1"}]}},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.date&&(t.beginTime=t.date[0],t.endTime=t.date[1]),e.next=5,o["a"](t);case 5:n=e.sent,this.tableData=n,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),renderOperateType:function(e){var t=e.operateType,n="未知";switch(t){case"1":n="新增";break;case"2":n="修改";break;case"3":n="删除";break;case"4":n="查询";break;case"5":n="退出登录";break;default:}return n}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},f709:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"地区"}},[n("x-select",{attrs:{"show-default":"",url:"/deliverArea/options"},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-refresh"},on:{click:e.handleStats}},[e._v("重新统计")])],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,"show-summary":!0,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-class-name":e.highlightChildren},on:{"expand-change":e.handleExpandChange}},[n("el-table-column",{attrs:{label:"日期",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.date.slice(0,10)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"areaName",label:"地区",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"taskName",label:"任务",align:"center","min-width":"150"}}),e._v(" "),n("el-table-column",{attrs:{prop:"totalTarget",label:"目标",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueNewFans",label:"进线",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"changeNewFans",label:"调粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"bannedNewFans",label:"封号",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"residue",label:"剩余(容量)",sortable:"",align:"center","min-width":"120"}}),e._v(" "),n("el-table-column",{attrs:{prop:"totalFans",label:"总粉",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"trueRepeatFans",label:"重复",sortable:"",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{label:"重复率",align:"center",sortable:"","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.repeatFansPro)+"%")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"单价",align:"center","sortablemin-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.price)+" 元")])]}}])})],1)],1)],1)},r=[],i=(n("c5f6"),n("96cf"),n("1da1")),s=(n("ac6a"),n("5df3"),n("4f7f"),n("4624")),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{date:(new Date).toISOString().slice(0,10)},loading:!1,tableData:{},expandedChildren:new Set}},created:function(){},methods:{queryReset:function(){this.queryParams={date:(new Date).toISOString().slice(0,10)},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["i"].getActualTaskStatsList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getSummaries:function(e){var t=e.columns,n=e.data,a=[];return t.forEach((function(e,t){if(0!==t){var r=n.map((function(t){return Number(t[e.property])}));r.every((function(e){return isNaN(e)}))?a[t]="--":a[t]=r.reduce((function(e,t){return e+t}),0)}else a[t]="合计"})),a},handleExpandChange:function(e,t){var n=this;t?e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.add(e.id)})):e.children&&e.children.length>0&&e.children.forEach((function(e){return n.expandedChildren.delete(e.id)}))},highlightChildren:function(e){var t=e.row;return this.expandedChildren.has(t.id)?"highlight-row":""},handleStats:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.queryParams.date,e.next=3,o["j"].stats(t);case 3:n=e.sent,0===n.code?this.$xMsgSuccess("重新统计成功"):this.$xMsgError(n.msg);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},c=l,u=(n("6b9d"),n("2877")),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},f794:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:add"],expression:"['system:menu:add']"}],attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.menuList,"row-key":"id",height:e.tableHeight,"tree-props":{children:"children"}}},[n("el-table-column",{attrs:{prop:"menuName",label:"菜单名称",width:"160"}}),e._v(" "),n("el-table-column",{attrs:{prop:"icon",label:"图标",width:"60",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[n("x-icon",{attrs:{icon:t.icon}})]}}])}),e._v(" "),n("el-table-column",{attrs:{prop:"sort",label:"排序",width:"60",align:"center"}}),e._v(" "),n("el-table-column",{attrs:{prop:"component",label:"组件路径","show-overflow-tooltip":!0}}),e._v(" "),n("el-table-column",{attrs:{prop:"permission",label:"权限标识","show-overflow-tooltip":!0}}),e._v(" "),n("el-table-column",{attrs:{prop:"isShow",label:"可见",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["1"==t.row.isShow?n("el-tag",{attrs:{type:"success"}},[e._v("显示")]):n("el-tag",{attrs:{type:"danger"}},[e._v("隐藏")])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:edit"],expression:"['system:menu:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:add"],expression:"['system:menu:add']"}],attrs:{size:"mini",type:"text",icon:"el-icon-plus"},on:{click:function(n){return e.handleAdd(t.row)}}},[e._v("新增")]),e._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:delete"],expression:"['system:menu:delete']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("a6dc"),l=n("89ee"),c={mixins:[s["a"]],components:{EditDialog:l["default"]},data:function(){return{tableHeightOptions:{page:!1},queryParams:{},loading:!1,menuList:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.getList();case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{onSystemCodeChange:function(e){this.queryParams.systemCode=e,this.getList()},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,o["getList"](this.queryParams);case 3:t=e.sent,this.menuList=t.data,this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleOk:function(){this.getList()},handleAdd:function(e){this.$refs.editDialog.add(e)},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(e){var t=this;this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then((function(){t.deleteData(e)})).catch((function(){}))},deleteData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["delMenu"](t.id);case 2:n=e.sent,0==n.code?(this.$xMsgSuccess("删除成功"),this.getList()):this.x.msgError("删除失败！"+n.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},u=c,d=n("2877"),m=Object(d["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},f7b9:function(e,t,n){"use strict";n("2c57")},f86d:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"welcome-container"},[n("div",{staticClass:"welcome-header"},[n("img",{staticClass:"avatar",attrs:{src:e.avatarUrl,alt:"Avatar"}}),e._v(" "),n("h1",[e._v("欢迎回来，"+e._s(e.name)+"！")]),e._v(" "),n("p",{staticClass:"welcome-message"},[e._v("很高兴见到你，祝你今天心情愉快！")]),e._v(" "),n("p",{staticClass:"version-info"},[e._v("版本号："+e._s(e.upgradeInfo.verName)+" ("+e._s(e.upgradeInfo.verCode)+")")])]),e._v(" "),n("div",{staticClass:"button-group"},[n("el-button",{attrs:{type:"primary",plain:"",round:"",icon:"el-icon-refresh"},on:{click:e.syncUserData}},[e._v("同步账户")]),e._v(" "),n("el-button",{attrs:{type:"success",plain:"",round:"",icon:"el-icon-user"},on:{click:e.center}},[e._v("个人中心")]),e._v(" "),n("el-button",{attrs:{type:"danger",plain:"",round:"",icon:"el-icon-switch-button"},on:{click:e.logout}},[e._v("退出登录")])],1)])},r=[],i=(n("8e6e"),n("ac6a"),n("456d"),n("96cf"),n("1da1")),s=(n("7f7f"),n("ade3")),o=n("3fa5"),l=n("2f62"),c=n("fae4"),u=n.n(c);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){Object(s["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p={data:function(){return{avatarUrl:u.a,upgradeInfo:{verName:"0.0.0",verCode:0}}},computed:m({},Object(l["c"])(["avatar","name","userId"])),created:function(){var e=this.$router.history.current.query;e.verName&&(this.upgradeInfo.verName=e.verName),e.verCode&&(this.upgradeInfo.verCode=e.verCode)},mounted:function(){this.avatar&&(this.avatarUrl=this.avatar);var e={name:this.name,userId:this.userId};o["a"]({type:"UserData",data:e})},methods:{center:function(){var e={url:window.location.origin};o["a"]({type:"OpenTab",data:e})},logout:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,this.$store.dispatch("Logout");case 3:o["a"]({type:"Logout"}),this.$xloading.hide(),this.$router.push("/login?redirect=".concat(this.$route.fullPath));case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),syncUserData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$xMsgInfo("账户开始同步，请等候同步通知！"),o["a"]({type:"SyncUserData"});case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},f=p,h=(n("c66f"),n("2877")),v=Object(h["a"])(f,a,r,!1,null,"2cfb8568",null);t["default"]=v.exports},fb9d:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"700px"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[n("el-card",{staticClass:"current-info-card",attrs:{shadow:"never"}},[n("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("当前账号信息")])]),e._v(" "),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("div",{staticClass:"info-item"},[n("span",{staticClass:"info-label"},[e._v("当前主号:")]),e._v(" "),n("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v(e._s(e.currentAccount))])],1)]),e._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticClass:"info-item"},[n("span",{staticClass:"info-label"},[e._v("渠道:")]),e._v(" "),n("el-tag",{attrs:{type:"success",size:"medium"}},[e._v(e._s(e.currentChannel))])],1)])],1)],1),e._v(" "),n("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"新主号",prop:"account"}},[n("el-input",{attrs:{placeholder:"请输入新的主号",clearable:"","show-word-limit":""},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}},[n("template",{slot:"prepend"},[n("i",{staticClass:"el-icon-user"})])],2),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        请输入要替换的新主号账号\n      ")])],1),e._v(" "),n("el-form-item",{attrs:{label:"换号类型",prop:"changeAccountType"}},[n("x-radio",{attrs:{options:e.changeAccountOptions},model:{value:e.form.changeAccountType,callback:function(t){e.$set(e.form,"changeAccountType",t)},expression:"form.changeAccountType"}}),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        新号继承旧号进线数: 当新号的计数器是从旧号的进线数开始计数是选择这个。\n      ")]),e._v(" "),n("div",{staticClass:"form-tip"},[n("i",{staticClass:"el-icon-info"}),e._v("\n        新号不继承旧号进线数: 当新号的计数器是从0开始计数是选择这个。\n      ")])],1)],1)],1)},r=[],i=(n("96cf"),n("1da1")),s=n("365c"),o={data:function(){return{title:"换号操作",visible:!1,loading:!1,currentRow:null,form:{id:null,account:"",changeAccountType:1},changeAccountOptions:[{label:"新号继承旧号进线数",value:1},{label:"新号不继承旧号进线数",value:2}],rules:{account:[{required:!0,message:"请输入新主号",trigger:"blur"}]}}},computed:{currentAccount:function(){return this.currentRow?this.currentRow.account:""},currentChannel:function(){return this.currentRow?this.currentRow.cName?"".concat(this.currentRow.cName," (").concat(this.currentRow.cId,")"):this.currentRow.cId:""}},methods:{open:function(e){this.currentRow=e,this.form.id=e.id,this.title="换号操作 - ".concat(e.account),this.visible=!0,this.resetForm()},resetForm:function(){var e=this;this.form={id:this.currentRow?this.currentRow.id:null,account:"",changeAccountType:1},this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},submitForm:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(n){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=3;break}return t.next=3,e.handleChangeAccount();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleChangeAccount:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm('确认将主号 "'.concat(this.currentAccount,'" 换为 "').concat(this.form.account,'" 吗？'),"换号确认",{type:"warning",confirmButtonText:"确认换号",cancelButtonText:"取消"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.loading=!0,n={id:t.form.id,account:t.form.account,changeAccountType:t.form.changeAccountType},e.next=5,s["l"].change(n);case 5:a=e.sent,0===a.code?(t.$xMsgSuccess("换号成功"),t.visible=!1,t.$emit("ok")):t.$xMsgError("换号失败："+a.msg),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](0),t.$xMsgError("换号失败："+e.t0.message);case 12:return e.prev=12,t.loading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,9,12,15]])})))).catch((function(){}));case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},l=o,c=(n("4771"),n("2877")),u=Object(c["a"])(l,a,r,!1,null,"83648c42",null);t["default"]=u.exports},fcb2:function(e,t,n){"use strict";n("179f")},fde5:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-card",[n("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[n("el-form-item",{attrs:{label:"所属用户"}},[n("x-select",{attrs:{"show-default":"",url:"/fBUser/options"},model:{value:e.queryParams.fbUserId,callback:function(t){e.$set(e.queryParams,"fbUserId",t)},expression:"queryParams.fbUserId"}})],1),e._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),n("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[n("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),n("el-table-column",{attrs:{prop:"id",label:"业务Id",align:"center","min-width":"100"}}),e._v(" "),n("el-table-column",{attrs:{prop:"name",label:"业务名称",align:"center","min-width":"200"}}),e._v(" "),n("el-table-column",{attrs:{prop:"createOn",label:"抓取时间",align:"center","min-width":"150"}})],1)],1),e._v(" "),n("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},r=[],i=(n("96cf"),n("1da1")),s=n("4624"),o=n("365c"),l={components:{},mixins:[s["a"]],data:function(){return{queryParams:{fbUserId:-1},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={fbUserId:-1},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["t"].getList(t);case 4:n=e.sent,this.tableData=n,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.$xloading.show(),e.next=3,o["t"].delFBBusinesses(t.id);case 3:a=e.sent,n.$xloading.hide(),0==a.code?(n.$refs.table.refresh(),n.$xMsgSuccess("删除成功")):n.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){n.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports}}]);