<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select show-default v-model="queryParams.areaId" url="/deliverArea/options"></x-select>
        </el-form-item>
        <el-form-item label="投放时段">
          <el-date-picker
            v-model="queryParams.date"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar"> </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :show-summary="true"
        :header-cell-style="{ fontSize: '14px' }"
        :cell-style="{ fontSize: '14px' }"
        :row-class-name="tableRowClassName"
        border
      >
        <el-table-column
          v-for="(col, index) in columns"
          :key="index"
          :prop="col.prop"
          :label="col.label"
          align="center"
          :min-width="col.width"
        >
          <template #default="scope">
            <task-info
              v-if="col.prop === 'taskName'"
              :taskId="scope.row['taskId']"
              :taskName="scope.row[col.prop]"
              :areaName="scope.row['areaName']"
            ></task-info>
            <!-- 其他数据直接显示 -->
            <span v-else>
              {{ scope.row[col.prop] || 0 }}
            </span>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
    <countdown ref="timer" :table="$refs.table"></countdown>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import Countdown from '@/components/Page/Countdown'
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverTaskInFansSpeedApi } from '@/api'
export default {
  components: {
    TaskInfo,
    Countdown,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      columns: [],
    }
  },
  created() {},
  beforeRouteLeave(to, from, next) {
    this.$refs.timer.stopAutoRefresh() // 手动调用子组件方法
    next()
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getColumns() {
      this.$xloading.show()
      const res = await deliverTaskInFansSpeedApi.getTaskInFansColumns()
      if (res && res.code == 0) {
        this.columns = res.data
      } else this.$xMsgError(res.msg)
      this.$xloading.hide()
    },
    async getList(params) {
      this.$xloading.show()
      //await this.getColumns()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await deliverTaskInFansSpeedApi.getList(params)
      this.columns = res.columns
      this.tableData = res
      this.$xloading.hide()
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD 00:00:00')
      const endDate = dayjs().add(1, 'day').format('YYYY-MM-DD 00:00:00')
      this.queryParams.date = [beginDate, endDate]
    },
    tableRowClassName({ row }) {
      if (row.newFans > 0) {
        return 'highlight-row' // 添加高亮 class
      }
      return ''
    },
  },
}
</script>

<style scoped>
/* 样式1：调整字体大小、行高、间距 */
.table-cell-content {
  line-height: 0.5; /* 行高 */
}

/* 高亮行背景 */
.highlight-row {
  background-color: rgba(255, 235, 59, 0.4) !important; /* 浅黄色高亮 */
}
</style>
